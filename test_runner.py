
import json
import csv
import random
import pandas as pd
import heapq
from collections import defaultdict
import os

TURN_COST = 1
LOAD_UNLOAD_TIME = 1
GRID_SIZE = (21, 21) # 可用网格大小为X，1-20,Y，1-20
CSV_PATH = "agv_trajectory_v2.csv"
COLLISION_PATH = "agv_collisions_v2.csv"
SCORING_PATH = "scoring_results_v2.csv"

# 评分系统常量
MAX_TIME = 300  # 5分钟 = 300秒
BASE_SCORE = 100  # 基础分数
BONUS_SCORE = 20  # 额外附加分
URGENT_BONUS = 10  # 高优先级任务奖励
URGENT_PENALTY = 5  # 高优先级任务惩罚
COLLISION_PENALTY = 10  # AGV碰撞惩罚

random.seed(42)  # 固定种子确保重现性

def manhattan(p1, p2):
    """
    该参考方法
    用于计算曼哈顿距离
    """
    return abs(p1[0] - p2[0]) + abs(p1[1] - p2[1])

def get_orientation(from_pos, to_pos):
    """
    用于比较朝向，计算转向角度
    """
    dx, dy = to_pos[0] - from_pos[0], to_pos[1] - from_pos[1]
    if dx > 0:
        return 0
    elif dx < 0:
        return 180
    elif dy > 0:
        return 90
    elif dy < 0:
        return 270
    else:
        return None

def a_star(start, goal, obstacles, grid_size=(21, 21)):
    """
    参考的AGV路径规划的A*算法，
    AGV仅可以在X，Y方向移动，代价函数包含取放料时间+转向时间+路程时间
    obstacles可以增加动态的障碍信息，默认为地图输入障碍信息
    """
    def neighbors(pos):
        x, y = pos
        for dx, dy in [(1,0), (-1,0), (0,1), (0,-1)]:
            nx, ny = x + dx, y + dy
            if 1 <= nx <= grid_size[0] and 1 <= ny <= grid_size[1] and (nx, ny) not in obstacles:
                yield (nx, ny)

    frontier = []
    heapq.heappush(frontier, (manhattan(start, goal), 0, start, [start]))
    visited = set()

    while frontier:
        _, cost, current, path = heapq.heappop(frontier)
        if current == goal:
            return path
        if current in visited:
            continue
        visited.add(current)
        for neighbor in neighbors(current):
            if neighbor not in visited:
                heapq.heappush(frontier, (cost + 1 + manhattan(neighbor, goal), cost + 1, neighbor, path + [neighbor]))
    return []
def simulate_path_strict(name, path, start_time, initial_pitch, loaded, destination, emergency):
    """
    参考的路径模拟方法
    从路径生成每一秒AGV状态，严格满足以下规则：
    - 原地转向需1秒
    - X/Y移动每次1秒，不能同时转向和移动
    - 每条记录代表一个时间戳的AGV状态
    """
    steps = []
    t = start_time
    pitch = initial_pitch
    last = path[0]

    for current in path[1:]:
        new_pitch = get_orientation(last, current)

        # 若朝向变化，则先转向
        if new_pitch != pitch:
            steps.append({
                "timestamp": t,
                "name": name,
                "X": last[0],
                "Y": last[1],
                "pitch": new_pitch,
                "loaded": loaded,
                "destination": destination if loaded else "",
                "Emergency": emergency
            })
            pitch = new_pitch
            t += 1

        # 移动到下一个位置
        steps.append({
            "timestamp": t,
            "name": name,
            "X": current[0],
            "Y": current[1],
            "pitch": pitch,
            "loaded": loaded,
            "destination": destination if loaded else "",
            "Emergency": emergency
        })
        last = current
        t += 1

    return steps, t, pitch, last

def reserve_simulated_steps(steps, reservation_table, agv_name):
    """
    把AGV的路径记录写入 reservation_table，键为 (timestamp, (x, y))。
    用于后续路径冲突判断。
    """
    for step in steps:
        key = (step["timestamp"], (step["X"], step["Y"]))
        reservation_table[key] = agv_name


def init_csv(agv_list):
    """
        初始化CSV_PATH的AGV轨迹文件，timestamp=0需要在最终轨迹文件中体现。
        """
    with open(CSV_PATH, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(["timestamp", "name", "X", "Y", "pitch", "loaded", "destination", "Emergency"])
        for agv in agv_list:
            writer.writerow([
                0,
                agv["id"],
                agv["pose"][0],
                agv["pose"][1],
                agv["pitch"],
                "false",
                "",
                "false"
            ])

def append_to_csv(steps):
    with open(CSV_PATH, 'a', newline='') as f:
        writer = csv.writer(f)
        for step in steps:
            writer.writerow([
                step["timestamp"],
                step["name"],
                step["X"],
                step["Y"],
                step["pitch"],
                str(step["loaded"]).lower(),
                step["destination"],
                str(step["Emergency"]).lower()
            ])

def get_pickup_coord(start_point_name, original_coord):
    """
        起始点与终点为AGV行使的障碍位置。
        "Tiger", "Dragon", "Horse"取料点位置需要是起始任务点X+1的偏置位置
        其余任务起始点取料点位置需要是起始任务点X-1的偏置位置
     """
    if start_point_name in ["Tiger", "Dragon", "Horse"]:
        return (original_coord[0] + 1, original_coord[1])
    else:
        return (original_coord[0] - 1, original_coord[1])

def get_delivery_options(dest_coord):
    """
        终点为AGV行使的障碍位置。
        每个终点卸料点位置共4个可选位置，分别为任务终点坐标(x,y)的 (x+1, y), (x-1, y), (x, y+1), (x, y-1)的偏置位置
    """
    x, y = dest_coord
    return [(x+1, y), (x-1, y), (x, y+1), (x, y-1)]

def is_head_on_swap_conflict(path_steps, reservation_table):
    """
    AGV常见的冲突类型之一，该代码提供示例，可能存在冲突检测不完善的地方，需要进一步优化
    防止对穿型冲突：AGV A at (t, pos1) and AGV B at (t, pos2) then swap at (t+1)
    """
    for i in range(len(path_steps) - 1):
        now = (path_steps[i]["timestamp"], (path_steps[i]["X"], path_steps[i]["Y"]))
        nxt = (path_steps[i+1]["timestamp"], (path_steps[i+1]["X"], path_steps[i+1]["Y"]))
        for (t, pos), other_agv in reservation_table.items():
            if t == now[0] and pos == nxt[1]:
                if (t + 1, now[1]) in reservation_table and reservation_table[(t + 1, now[1])] == other_agv:
                    return True
    return False

def is_conflict(path_steps, reservation_table):
    """
        用于冲突检测判断的参考，如发生冲突，可以采取类比的方式进行检查
    """
    for step in path_steps:
        key = (step["timestamp"], (step["X"], step["Y"]))
        if key in reservation_table:
            return True
    if is_head_on_swap_conflict(path_steps, reservation_table):
        return True
    return False

def assign_tasks(task_file, map_file):
    """
        该示例参考方法
        不考虑A任务队列的紧急任务的处理，以及取料任务序列的优化
        仅按照随机性选择任务，并保证每个子队列任务的顺序不被打乱
    """
    all_tasks = []
    with open(task_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            all_tasks.append({
                "task_id": row["task_id"],
                "start_point": row["start_point"].strip(),
                "end_point": row["end_point"].strip(),
                "priority": row["priority"],
                "remaining_time": int(row["remaining_time"]) if row["remaining_time"] not in [None, "", "None"] else None
            })

    # --- 地图数据 ---
    start_points, end_points, agv_list = {}, {}, []
    with open(map_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            t, name = row["type"].strip(), row["name"].strip()
            x, y = int(row["x"]), int(row["y"])
            if t == "start_point":
                start_points[name] = (x, y)
            elif t == "end_point":
                end_points[name] = (x, y)
            elif t == "agv":
                agv_list.append({
                    "id": name,
                    "pose": (x, y),
                    "pitch": int(row["pitch"])
                })

    init_csv(agv_list)

    # --- 构建起始点任务队列（FIFO） ---
    task_queues = {}
    for task in all_tasks:
        sp = task["start_point"]
        if sp not in task_queues:
            task_queues[sp] = []
        task_queues[sp].append(task)

    # --- 初始化AGV状态 ---
    agv_count = len(agv_list)
    agv_queue = [agv["id"] for agv in agv_list[:agv_count]]
    agv_states = {
        agv["id"]: {
            "pos": tuple(agv["pose"]),
            "pitch": agv["pitch"],
            "time": 1,
            "home": tuple(agv["pose"])
        } for agv in agv_list[:agv_count]
    }

    reservation_table = {}
    assigned_tasks = []

    while any(task_queues[sp] for sp in task_queues):
        progress = False
        agv_progress = {agv: False for agv in agv_queue}

        for agv in agv_queue:
            state = agv_states[agv]

            shuffled_starts = list(task_queues.keys())
            random.shuffle(shuffled_starts)

            for sp in shuffled_starts:
                if not task_queues[sp]:
                    continue

                task = task_queues[sp][0]  # ✅ FIFO
                ep = task["end_point"]
                priority = task["priority"]
                emergency = True if priority.lower() == "urgent" else False
                taskid = task["task_id"]

                start_coord = get_pickup_coord(sp, start_points[sp])
                end_coord_main = end_points[ep]
                delivery_candidates = get_delivery_options(end_coord_main)

                best_path = None
                best_goal = None
                best_pitch = None
                best_t = float("inf")

                for d in delivery_candidates:
                    path_to_pick = a_star(state["pos"], start_coord, set(start_points.values()) | set(end_points.values()))
                    if not path_to_pick:
                        continue

                    steps1, t1, pitch1, pos1 = simulate_path_strict(agv, path_to_pick, state["time"], state["pitch"], False, "", False)
                    if is_conflict(steps1, reservation_table):
                        continue

                    if emergency:
                        x=1

                    steps1.append({
                        "timestamp": t1,
                        "name": agv,
                        "X": pos1[0],
                        "Y": pos1[1],
                        "pitch": pitch1,
                        "loaded": "true",
                        "destination": ep,
                        "Emergency": emergency,
                        "task-id": taskid
                    })
                    t1 += 1

                    path_to_deliver = a_star(pos1, d, set(start_points.values()) | set(end_points.values()))
                    if not path_to_deliver:
                        continue

                    steps2, t2, pitch2, pos2 = simulate_path_strict(agv, path_to_deliver, t1, pitch1, True, ep, emergency)
                    if is_conflict(steps2, reservation_table):
                        continue

                    steps2.append({
                        "timestamp": t2,
                        "name": agv,
                        "X": pos2[0],
                        "Y": pos2[1],
                        "pitch": pitch2,
                        "loaded": "false",
                        "destination": "",
                        "Emergency": "false",
                        "task-id": taskid
                    })
                    t2 += 1

                    total_time = t2
                    if total_time < best_t:
                        best_t = total_time
                        best_path = steps1 + steps2
                        best_goal = pos2
                        best_pitch = pitch2

                if best_path:
                    reserve_simulated_steps(best_path, reservation_table, agv)
                    append_to_csv(best_path)
                    assigned_tasks.append({
                        "agv": agv,
                        "start_point": sp,
                        "end_point": ep,
                        "priority": priority,
                        "start_time": state["time"],
                        "agv_start_pose": state["pos"],
                        "agv_start_orientation": state["pitch"]
                    })
                    agv_states[agv] = {
                        "pos": best_goal,
                        "pitch": best_pitch,
                        "time": best_t,
                        "home": state["home"]
                    }
                    task_queues[sp].pop(0)  # ✅ 严格FIFO

                    progress = True
                    agv_progress[agv] = True
                    break  # ✅ AGV分配一次任务后退出，进行下一个AGV尝试

        # ⭐ 若所有AGV无法调度，尝试回原点避让或自增时间
        if not progress:
            print("[WARN] 所有AGV当前都无法执行任务，尝试回归起始点避让。")
            for agv in agv_queue:
                if not agv_progress[agv]:
                    state = agv_states[agv]
                    if state["pos"] != state["home"]:
                        path_home = a_star(state["pos"], state["home"], set(start_points.values()) | set(end_points.values()))
                        if path_home:
                            steps, t_end, pitch_end, pos_end = simulate_path_strict(
                                agv, path_home, state["time"], state["pitch"], "false", "", "false"
                            )
                            reserve_simulated_steps(steps, reservation_table, agv)
                            append_to_csv(steps)
                            agv_states[agv] = {
                                "pos": pos_end,
                                "pitch": pitch_end,
                                "time": t_end,
                                "home": state["home"]
                            }
                        else:
                            agv_states[agv]["time"] += 1
                    else:
                        agv_states[agv]["time"] += 1

    final_time = max(state["time"] for state in agv_states.values())
    finalize_trajectory_csv(agv_states, final_time)
    print(f"[INFO] 分配完成，共分配任务数：{len(assigned_tasks)}。当前分配方案（可能有碰撞）任务最短完成时间为： {final_time} s")
    return assigned_tasks


# 定义转换函数
def to_lower_str(x):
    return str(x).lower() if pd.notna(x) else x

def finalize_trajectory_csv(agv_states, final_time, csv_path=CSV_PATH):
    """
    对所有 AGV 的轨迹进行时间补全并排序输出。
    需要输出所有AGV从0时刻到最后一个任务结束时刻的所有AGV轨迹信息，即使AGV原地不动
    若在过程中未补充，需要在最终补全数据
    """

    with open(csv_path, 'a', newline='') as f:
        writer = csv.writer(f)
        for agv_id, state in agv_states.items():
            last_time = state["time"]
            for t in range(last_time, final_time + 1):
                writer.writerow([
                    t,
                    agv_id,
                    state["pos"][0],
                    state["pos"][1],
                    state["pitch"],
                    "false", "", "false"
                ])

    # 排序逻辑,最终输出的loaded、Emergency对应的列中“true","false"必须保持为小写，否则会影响评分
    # 读取CSV时直接转换
    col_names = pd.read_csv(csv_path, nrows=0).columns.tolist()
    df =pd.read_csv(csv_path,converters={col_names[5]: to_lower_str, col_names[7]: to_lower_str})
    df.sort_values(by=["timestamp", "name"], inplace=True)
    df.to_csv(csv_path, index=False)
    print(f"[INFO] Final all AGV trajectory file with timestamp: saved to {csv_path}")

    # 基础碰撞类型检测方法
    detect_collisions(csv_path='agv_trajectory.csv', output_path='agv_collisions.csv')



def detect_collisions(csv_path, output_path):
    """
        该示例参考方法
        提供对最终输出的AGV轨迹CSV文件进行轨迹冲突判断，该部分判断可能存在不完善的方面，仅供参考
    """
    position_states = defaultdict(dict)  # {timestamp: {(x, y): agv_name}}
    agv_positions = defaultdict(dict)  # {agv: {timestamp: (x,y)}}

    # 读取CSV轨迹数据
    with open(csv_path, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            timestamp = int(row['timestamp'])
            x, y = int(row['X']), int(row['Y'])
            agv = row['name']
            position_states[timestamp][(x, y)] = agv
            agv_positions[agv][timestamp] = (x, y)

    collisions = []

    # ---- 静态冲突 ----
    for timestamp in position_states:
        pos_counts = defaultdict(list)
        for pos, agv in position_states[timestamp].items():
            pos_counts[pos].append(agv)

        for pos, agvs in pos_counts.items():
            if len(agvs) > 1:
                collisions.append({
                    "timestamp": timestamp,
                    "X": pos[0],
                    "Y": pos[1],
                    "type": "static",
                    "AGVs": ", ".join(agvs)
                })

    # ---- 对穿冲突（去重）----
    seen_crossings = set()
    for agv1 in agv_positions:
        for agv2 in agv_positions:
            if agv1 >= agv2:
                continue  # 避免重复和自身
            for t in agv_positions[agv1]:
                if (t + 1 not in agv_positions[agv1]) or (t + 1 not in agv_positions[agv2]):
                    continue

                p1_now = agv_positions[agv1][t]
                p1_next = agv_positions[agv1][t + 1]
                p2_now = agv_positions[agv2][t]
                p2_next = agv_positions[agv2][t + 1]

                if p1_now == p2_next and p2_now == p1_next:
                    key = (t, tuple(sorted([agv1, agv2])))
                    if key not in seen_crossings:
                        seen_crossings.add(key)
                        collisions.append({
                            "timestamp": t,
                            "X": p1_now[0],
                            "Y": p1_now[1],
                            "type": "crossing",
                            "AGVs": f"{agv1}, {agv2}"
                        })

    # ---- 保存检测结果 ----
    with open(output_path, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=["timestamp", "X", "Y", "type", "AGVs"])
        writer.writeheader()
        writer.writerows(collisions)

    print(f"[INFO] Detected {len(collisions)} collision events. Saved to {output_path}")


if __name__ == '__main__':
    # 基础任务分配路径规划（不考虑紧急任务优先级）、任务路径规划（基础A*）、简易碰撞检测与避障策略（无法保障0碰撞）
    
    # Construct the path to the data files
    data_dir = os.path.join(os.path.dirname(__file__), '赛题附件-输入输出格式说明')
    task_file = os.path.join(data_dir, '附件2——task_csv.csv')
    map_file = os.path.join(data_dir, '附件1——map_data.csv')

    result = assign_tasks(task_file=task_file, map_file=map_file)


    # 接收输入
    # for line in sys.stdin:
    #     try:
    #         request = json.loads(line)
    #         task_file = request.get("task_file")
    #         map_file = request.get("map_file")
    #
    #         # 调用主逻辑
    #         result = assign_tasks(task_file, map_file)
    #
    #         # 返回结果
    #         response = {"status": "success", "trajectory": result}
    #         print(json.dumps(response))
    #
    #     except json.JSONDecodeError:
    #         response = {"status": "error", "message": "Invalid JSON input"}
    #         print(json.dumps(response))
    #     except Exception as e:
    #         response = {"status": "error", "message": str(e)}
    #         print(json.dumps(response))
    pass
