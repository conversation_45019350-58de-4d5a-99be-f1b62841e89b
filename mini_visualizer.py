import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# 读取数据
map_data = pd.read_csv("赛题附件-输入输出格式说明/附件1——map_data.csv")
trajectory = pd.read_csv("agv_trajectory.csv")

# 解析地图数据
start_points = map_data[map_data['type'] == 'start_point']
end_points = map_data[map_data['type'] == 'end_point']

# 创建可视化函数
def show_agv_at_time(t=0):
    """显示指定时间的AGV状态"""
    plt.figure(figsize=(12, 10))
    
    # 绘制起始点和终点
    plt.scatter(start_points['x'], start_points['y'], c='green', s=400, marker='s', alpha=0.7, label='Start')
    plt.scatter(end_points['x'], end_points['y'], c='red', s=400, marker='s', alpha=0.7, label='End')
    
    # 绘制AGV
    current_agvs = trajectory[trajectory['timestamp'] == t]
    colors = plt.cm.Set3(np.linspace(0, 1, len(current_agvs)))
    
    for i, (_, agv) in enumerate(current_agvs.iterrows()):
        # AGV圆圈
        plt.scatter(agv['X'], agv['Y'], c=[colors[i]], s=300, marker='o', 
                   edgecolor='black', linewidth=2, alpha=0.9)
        # AGV名称
        plt.text(agv['X'], agv['Y']+0.7, agv['name'], ha='center', va='center', 
                fontsize=9, weight='bold')
        # 状态信息
        status = f"Load:{agv['loaded']}"
        if agv['destination'] and agv['destination'] != '':
            status += f"\\nTo:{agv['destination']}"
        if agv['Emergency'] == 'true':
            status += "\\n⚠️URGENT"
        plt.text(agv['X'], agv['Y']-1.0, status, ha='center', va='center', 
                fontsize=7, bbox=dict(boxstyle="round", facecolor=colors[i], alpha=0.6))
    
    plt.xlim(0, 21)
    plt.ylim(0, 21)
    plt.grid(True, alpha=0.3)
    plt.title(f'AGV System - Time: {t}s', fontsize=16, weight='bold')
    plt.legend()
    plt.tight_layout()
    plt.show()

# 显示不同时间点的状态
print("=== AGV系统可视化 ===")
print(f"总AGV数量: {len(trajectory['name'].unique())}")
print(f"模拟总时间: {trajectory['timestamp'].max()}秒")
print(f"总记录数: {len(trajectory)}")

# 检查碰撞
try:
    collisions = pd.read_csv("agv_collisions.csv")
    print(f"检测到碰撞: {len(collisions)}次")
except:
    print("未找到碰撞数据")

# 显示关键时间点
show_agv_at_time(0)    # 初始状态
show_agv_at_time(50)   # 50秒
show_agv_at_time(100)  # 100秒

# 轨迹热力图
plt.figure(figsize=(12, 10))
visit_count = trajectory.groupby(['X', 'Y']).size().reset_index(name='count')
scatter = plt.scatter(visit_count['X'], visit_count['Y'], 
                     c=visit_count['count'], s=visit_count['count']*3, 
                     cmap='Reds', alpha=0.8)
plt.colorbar(scatter, label='Visit Count')
plt.scatter(start_points['x'], start_points['y'], c='green', s=400, marker='s', alpha=0.9, label='Start')
plt.scatter(end_points['x'], end_points['y'], c='blue', s=400, marker='s', alpha=0.9, label='End')
plt.xlim(0, 21)
plt.ylim(0, 21)
plt.grid(True, alpha=0.3)
plt.title('AGV Trajectory Heatmap', fontsize=16, weight='bold')
plt.legend()
plt.tight_layout()
plt.show()

print("可视化完成！")
