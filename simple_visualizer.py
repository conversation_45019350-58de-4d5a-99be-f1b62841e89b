import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

def visualize_agv_system():
    """简化的AGV系统可视化"""
    # 读取数据
    map_data = pd.read_csv("赛题附件-输入输出格式说明/附件1——map_data.csv")
    trajectory = pd.read_csv("agv_trajectory.csv")
    
    # 解析地图数据
    start_points = map_data[map_data['type'] == 'start_point']
    end_points = map_data[map_data['type'] == 'end_point']
    agvs = map_data[map_data['type'] == 'agv']
    
    def plot_time(t=0):
        """绘制指定时间点的状态"""
        plt.figure(figsize=(10, 10))
        
        # 绘制起始点（绿色方块）
        plt.scatter(start_points['x'], start_points['y'], 
                   c='green', s=300, marker='s', alpha=0.7, label='起始点')
        for _, row in start_points.iterrows():
            plt.text(row['x'], row['y'], row['name'], ha='center', va='center', fontsize=8)
        
        # 绘制终点（红色方块）
        plt.scatter(end_points['x'], end_points['y'], 
                   c='red', s=300, marker='s', alpha=0.7, label='终点')
        for _, row in end_points.iterrows():
            plt.text(row['x'], row['y'], row['name'], ha='center', va='center', fontsize=6)
        
        # 绘制AGV当前位置
        current_pos = trajectory[trajectory['timestamp'] == t]
        colors = plt.cm.Set3(np.linspace(0, 1, len(current_pos)))
        
        for i, (_, agv) in enumerate(current_pos.iterrows()):
            plt.scatter(agv['X'], agv['Y'], c=[colors[i]], s=200, 
                       marker='o', edgecolor='black', linewidth=2)
            plt.text(agv['X'], agv['Y']+0.5, agv['name'], 
                    ha='center', va='center', fontsize=8, weight='bold')
            
            # 显示状态
            status = f"载货:{agv['loaded']}"
            if agv['destination']:
                status += f"\n→{agv['destination']}"
            if agv['Emergency'] == 'true':
                status += "\n⚠️紧急"
            plt.text(agv['X'], agv['Y']-0.8, status, ha='center', va='center', 
                    fontsize=6, bbox=dict(boxstyle="round", facecolor='white', alpha=0.8))
        
        plt.xlim(0, 21)
        plt.ylim(0, 21)
        plt.grid(True, alpha=0.3)
        plt.title(f'AGV系统状态 - 时间: {t}秒', fontsize=14, weight='bold')
        plt.xlabel('X坐标')
        plt.ylabel('Y坐标')
        plt.legend()
        plt.tight_layout()
        plt.show()
    
    # 绘制不同时间点
    plot_time(0)   # 初始状态
    plot_time(50)  # 50秒时状态
    plot_time(100) # 100秒时状态
    
    # 绘制轨迹热力图
    plt.figure(figsize=(10, 10))
    
    # 统计每个位置的访问次数
    visit_count = trajectory.groupby(['X', 'Y']).size().reset_index(name='count')
    
    # 绘制热力图
    plt.scatter(visit_count['X'], visit_count['Y'], 
               c=visit_count['count'], s=visit_count['count']*2, 
               cmap='Reds', alpha=0.7)
    plt.colorbar(label='访问次数')
    
    # 叠加地图信息
    plt.scatter(start_points['x'], start_points['y'], 
               c='green', s=300, marker='s', alpha=0.9, label='起始点')
    plt.scatter(end_points['x'], end_points['y'], 
               c='blue', s=300, marker='s', alpha=0.9, label='终点')
    
    plt.xlim(0, 21)
    plt.ylim(0, 21)
    plt.grid(True, alpha=0.3)
    plt.title('AGV轨迹热力图', fontsize=14, weight='bold')
    plt.xlabel('X坐标')
    plt.ylabel('Y坐标')
    plt.legend()
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    visualize_agv_system()
