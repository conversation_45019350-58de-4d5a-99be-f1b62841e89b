import json
import csv
import random
import pandas as pd
import heapq
from collections import defaultdict
import os
import time

TURN_COST = 1
LOAD_UNLOAD_TIME = 1
GRID_SIZE = (21, 21) # 可用网格大小为X，1-20,Y，1-20
CSV_PATH = "agv_trajectory_v3.csv"
COLLISION_PATH = "agv_collisions_v3.csv"
SCORING_PATH = "scoring_results_v3.csv"

# 评分系统常量
MAX_TIME = 300  # 5分钟 = 300秒
BASE_SCORE = 100  # 基础分数
BONUS_SCORE = 20  # 额外附加分
URGENT_BONUS = 10  # 高优先级任务奖励
URGENT_PENALTY = 5  # 高优先级任务惩罚
COLLISION_PENALTY = 10  # AGV碰撞惩罚

# 零碰撞优化参数（降低复杂度）
SAFETY_BUFFER = 1  # 安全缓冲时间（减少）
MAX_WAIT_TIME = 5  # 最大等待时间（减少）
CONGESTION_THRESHOLD = 2  # 拥堵阈值（减少）
FUTURE_CHECK_WINDOW = 10  # 未来检查窗口（减少）

random.seed(42)  # 固定种子确保重现性

class ZeroCollisionScoringSystem:
    """零碰撞优化评分系统"""
    
    def __init__(self):
        self.completed_tasks = []
        self.urgent_tasks = []
        self.collisions = []
        self.agv_status = {}
        self.last_task_completion_time = 0
        self.collision_avoidance_count = 0  # 避免碰撞的次数
        
    def add_collision_avoidance(self):
        """记录成功避免碰撞的次数"""
        self.collision_avoidance_count += 1
        
    def add_completed_task(self, task_info):
        """添加完成的任务"""
        self.completed_tasks.append(task_info)
        if task_info.get('completion_time', 0) > self.last_task_completion_time:
            self.last_task_completion_time = task_info.get('completion_time', 0)
            
    def add_urgent_task(self, task_info):
        """添加高优先级任务信息"""
        self.urgent_tasks.append(task_info)
        
    def add_collision(self, collision_info):
        """添加碰撞信息"""
        self.collisions.append(collision_info)
        if 'AGVs' in collision_info:
            agvs = collision_info['AGVs'].split(', ')
            for agv in agvs:
                self.agv_status[agv] = 'disappeared'
                
    def calculate_score(self):
        """计算总分数"""
        score_details = {
            'base_score': 0,
            'urgent_bonus': 0,
            'urgent_penalty': 0,
            'collision_penalty': 0,
            'total_score': 0,
            'completed_tasks_count': 0,
            'urgent_completed_on_time': 0,
            'urgent_failed': 0,
            'collision_count': len(self.collisions),
            'collision_avoidance_count': self.collision_avoidance_count,
            'last_completion_time': self.last_task_completion_time
        }
        
        # 1. 基础分数：正确运达的快件数量
        valid_completed_tasks = []
        for task in self.completed_tasks:
            agv = task.get('agv')
            collision_time = task.get('completion_time', 0)
            
            agv_collided_before_completion = False
            for collision in self.collisions:
                if agv in collision.get('AGVs', '') and collision.get('timestamp', 0) <= collision_time:
                    agv_collided_before_completion = True
                    break
                    
            if not agv_collided_before_completion and task.get('completion_time', 0) <= MAX_TIME:
                valid_completed_tasks.append(task)
                
        score_details['completed_tasks_count'] = len(valid_completed_tasks)
        score_details['base_score'] = len(valid_completed_tasks)
        
        # 2. 高优先级任务奖罚
        for urgent_task in self.urgent_tasks:
            remaining_time = urgent_task.get('remaining_time')
            completion_time = urgent_task.get('completion_time')
            agv = urgent_task.get('agv')
            
            if remaining_time is None:
                continue
                
            agv_collided = False
            for collision in self.collisions:
                if agv in collision.get('AGVs', '') and collision.get('timestamp', 0) <= completion_time:
                    agv_collided = True
                    break
            
            if agv_collided:
                score_details['urgent_failed'] += 1
                score_details['urgent_penalty'] += URGENT_PENALTY
            elif completion_time is not None and completion_time <= remaining_time:
                score_details['urgent_completed_on_time'] += 1
                score_details['urgent_bonus'] += URGENT_BONUS
            else:
                score_details['urgent_failed'] += 1
                score_details['urgent_penalty'] += URGENT_PENALTY
        
        # 3. AGV碰撞惩罚
        score_details['collision_penalty'] = len(self.collisions) * COLLISION_PENALTY
        
        # 4. 计算总分
        score_details['total_score'] = (
            score_details['base_score'] + 
            score_details['urgent_bonus'] - 
            score_details['urgent_penalty'] - 
            score_details['collision_penalty']
        )
        
        return score_details
        
    def save_scoring_results(self, filepath=SCORING_PATH):
        """保存评分结果到文件"""
        score_details = self.calculate_score()
        
        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['评分项目', '数值', '说明'])
            writer.writerow(['基础分数', score_details['base_score'], f"正确运达快件数量: {score_details['completed_tasks_count']}"])
            writer.writerow(['高优先级奖励', score_details['urgent_bonus'], f"按时完成高优先级任务: {score_details['urgent_completed_on_time']}"])
            writer.writerow(['高优先级惩罚', -score_details['urgent_penalty'], f"未按时完成高优先级任务: {score_details['urgent_failed']}"])
            writer.writerow(['碰撞惩罚', -score_details['collision_penalty'], f"碰撞次数: {score_details['collision_count']}"])
            writer.writerow(['总分', score_details['total_score'], f"满分120分(含20分附加分)"])
            writer.writerow(['最后完成时间', score_details['last_completion_time'], '用于平分时比较'])
            writer.writerow(['避免碰撞次数', score_details['collision_avoidance_count'], '成功避免的碰撞次数'])
            
        print(f"[INFO] 评分结果已保存到: {filepath}")
        print(f"[INFO] 总分: {score_details['total_score']}/120")
        print(f"[INFO] 碰撞次数: {score_details['collision_count']}")
        print(f"[INFO] 避免碰撞次数: {score_details['collision_avoidance_count']}")
        print(f"[INFO] 最后任务完成时间: {score_details['last_completion_time']}秒")
        
        return score_details

def manhattan(p1, p2):
    """计算曼哈顿距离"""
    return abs(p1[0] - p2[0]) + abs(p1[1] - p2[1])

def get_orientation(from_pos, to_pos):
    """计算朝向"""
    dx, dy = to_pos[0] - from_pos[0], to_pos[1] - from_pos[1]
    if dx > 0:
        return 0
    elif dx < 0:
        return 180
    elif dy > 0:
        return 90
    elif dy < 0:
        return 270
    else:
        return None

def enhanced_a_star(start, goal, obstacles, reservation_table, start_time, agv_name, max_wait=MAX_WAIT_TIME):
    """
    增强版A*算法，考虑时间维度和动态障碍
    """
    def neighbors(pos, timestamp):
        x, y = pos
        candidates = []
        
        # 四个方向移动
        for dx, dy in [(1,0), (-1,0), (0,1), (0,-1)]:
            nx, ny = x + dx, y + dy
            if 1 <= nx <= 20 and 1 <= ny <= 20 and (nx, ny) not in obstacles:
                candidates.append((nx, ny))
        
        # 原地等待（如果有必要避免冲突）
        candidates.append(pos)
        
        # 过滤掉被预约的位置
        valid_candidates = []
        for candidate in candidates:
            conflict = False
            # 检查未来几个时间步的冲突
            for t_offset in range(SAFETY_BUFFER):
                check_time = timestamp + t_offset + 1
                if (check_time, candidate) in reservation_table:
                    if reservation_table[(check_time, candidate)] != agv_name:
                        conflict = True
                        break
            
            if not conflict:
                valid_candidates.append(candidate)
        
        return valid_candidates

    frontier = []
    heapq.heappush(frontier, (manhattan(start, goal), 0, start_time, start, [start]))
    visited = set()

    while frontier:
        _, cost, current_time, current_pos, path = heapq.heappop(frontier)
        
        if current_pos == goal:
            return path, current_time
            
        state = (current_pos, current_time)
        if state in visited:
            continue
        visited.add(state)
        
        # 防止路径过长
        if current_time - start_time > max_wait + len(path) * 2:
            continue
            
        for neighbor in neighbors(current_pos, current_time):
            if neighbor == current_pos:
                # 原地等待
                new_cost = cost + 1
                new_time = current_time + 1
                new_path = path + [neighbor] if len(path) == 0 or path[-1] != neighbor else path
            else:
                # 移动到新位置
                new_cost = cost + 1
                new_time = current_time + 1
                new_path = path + [neighbor]
            
            heuristic = manhattan(neighbor, goal)
            priority = new_cost + heuristic
            
            heapq.heappush(frontier, (priority, new_cost, new_time, neighbor, new_path))
    
    return [], start_time  # 无法找到路径

def safe_simulate_path_strict(name, path, start_time, initial_pitch, loaded, destination, emergency, reservation_table):
    """
    安全路径模拟，确保不会产生冲突
    """
    if not path or len(path) < 2:
        return [], start_time, initial_pitch, path[0] if path else None

    steps = []
    t = start_time
    pitch = initial_pitch
    last = path[0]

    for current in path[1:]:
        new_pitch = get_orientation(last, current)

        # 检查转向时的安全性
        if new_pitch != pitch and new_pitch is not None:
            # 检查转向时间的冲突
            if (t, last) in reservation_table and reservation_table[(t, last)] != name:
                # 等待直到安全
                while (t, last) in reservation_table and reservation_table[(t, last)] != name:
                    t += 1
                    if t > MAX_TIME:
                        return [], t, pitch, last

            steps.append({
                "timestamp": t,
                "name": name,
                "X": last[0],
                "Y": last[1],
                "pitch": new_pitch,
                "loaded": loaded,
                "destination": destination if loaded else "",
                "Emergency": emergency
            })
            pitch = new_pitch
            t += 1

        # 检查移动时的安全性
        if (t, current) in reservation_table and reservation_table[(t, current)] != name:
            # 等待直到安全
            wait_count = 0
            while (t, current) in reservation_table and reservation_table[(t, current)] != name:
                t += 1
                wait_count += 1
                if t > MAX_TIME or wait_count > MAX_WAIT_TIME:
                    return [], t, pitch, last

        steps.append({
            "timestamp": t,
            "name": name,
            "X": current[0],
            "Y": current[1],
            "pitch": pitch,
            "loaded": loaded,
            "destination": destination if loaded else "",
            "Emergency": emergency
        })
        last = current
        t += 1

    return steps, t, pitch, last

def check_comprehensive_conflicts(path_steps, reservation_table, agv_name):
    """
    全面的冲突检测，包括静态冲突、动态冲突和对穿冲突
    """
    conflicts = []

    for i, step in enumerate(path_steps):
        timestamp = step["timestamp"]
        pos = (step["X"], step["Y"])

        # 1. 静态冲突检测
        if (timestamp, pos) in reservation_table:
            if reservation_table[(timestamp, pos)] != agv_name:
                conflicts.append(f"静态冲突: 时间{timestamp}, 位置{pos}")

        # 2. 对穿冲突检测
        if i > 0:
            prev_step = path_steps[i-1]
            prev_pos = (prev_step["X"], prev_step["Y"])

            # 检查是否有其他AGV在同一时间从当前位置移动到前一个位置
            if (timestamp-1, pos) in reservation_table and (timestamp, prev_pos) in reservation_table:
                other_agv_prev = reservation_table.get((timestamp-1, pos))
                other_agv_curr = reservation_table.get((timestamp, prev_pos))
                if other_agv_prev == other_agv_curr and other_agv_prev != agv_name:
                    conflicts.append(f"对穿冲突: 时间{timestamp-1}-{timestamp}, AGV {agv_name} 与 {other_agv_prev}")

        # 3. 未来冲突预测
        for future_offset in range(1, SAFETY_BUFFER + 1):
            future_time = timestamp + future_offset
            if (future_time, pos) in reservation_table:
                if reservation_table[(future_time, pos)] != agv_name:
                    conflicts.append(f"未来冲突: 时间{future_time}, 位置{pos}")

    return conflicts

def reserve_path_safely(path_steps, reservation_table, agv_name, scoring_system):
    """
    安全地预约路径，如果发现冲突则不预约
    """
    conflicts = check_comprehensive_conflicts(path_steps, reservation_table, agv_name)

    if conflicts:
        print(f"[WARN] AGV {agv_name} 路径存在冲突: {conflicts[:3]}...")  # 只显示前3个冲突
        scoring_system.add_collision_avoidance()
        return False

    # 没有冲突，安全预约
    for step in path_steps:
        key = (step["timestamp"], (step["X"], step["Y"]))
        reservation_table[key] = agv_name

    return True

def get_pickup_coord(start_point_name, original_coord):
    """获取取料点坐标"""
    if start_point_name in ["Tiger", "Dragon", "Horse"]:
        return (original_coord[0] + 1, original_coord[1])
    else:
        return (original_coord[0] - 1, original_coord[1])

def get_delivery_options(dest_coord):
    """获取卸料点选项"""
    x, y = dest_coord
    return [(x+1, y), (x-1, y), (x, y+1), (x, y-1)]

def find_safest_delivery_point(agv_pos, delivery_candidates, reservation_table, current_time, agv_name):
    """找到最安全的卸货点"""
    best_candidate = None
    best_score = float('inf')

    for candidate in delivery_candidates:
        # 检查候选点是否在有效范围内
        if not (1 <= candidate[0] <= 20 and 1 <= candidate[1] <= 20):
            continue

        # 计算距离成本
        distance_cost = manhattan(agv_pos, candidate)

        # 计算安全成本（检查未来时间窗口的冲突）
        safety_cost = 0
        for t in range(current_time, current_time + FUTURE_CHECK_WINDOW):  # 减少检查窗口
            if (t, candidate) in reservation_table:
                if reservation_table[(t, candidate)] != agv_name:
                    safety_cost += 10  # 高安全成本

            # 简化周围位置检查
            for dx, dy in [(1,0), (-1,0), (0,1), (0,-1)]:
                neighbor = (candidate[0] + dx, candidate[1] + dy)
                if (t, neighbor) in reservation_table:
                    if reservation_table[(t, neighbor)] != agv_name:
                        safety_cost += 1

        total_score = distance_cost + safety_cost
        if total_score < best_score:
            best_score = total_score
            best_candidate = candidate

    return best_candidate

def init_csv(agv_list):
    """初始化CSV文件"""
    with open(CSV_PATH, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(["timestamp", "name", "X", "Y", "pitch", "loaded", "destination", "Emergency"])
        for agv in agv_list:
            writer.writerow([
                0,
                agv["id"],
                agv["pose"][0],
                agv["pose"][1],
                agv["pitch"],
                "false",
                "",
                "false"
            ])

def append_to_csv(steps):
    """追加步骤到CSV文件"""
    with open(CSV_PATH, 'a', newline='') as f:
        writer = csv.writer(f)
        for step in steps:
            writer.writerow([
                step["timestamp"],
                step["name"],
                step["X"],
                step["Y"],
                step["pitch"],
                str(step["loaded"]).lower(),
                step["destination"],
                str(step["Emergency"]).lower()
            ])

def zero_collision_assign_tasks(task_file, map_file):
    """
    零碰撞任务分配算法
    采用更保守的策略确保不发生碰撞
    """
    # 初始化零碰撞评分系统
    scoring_system = ZeroCollisionScoringSystem()

    all_tasks = []
    with open(task_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            all_tasks.append({
                "task_id": row["task_id"],
                "start_point": row["start_point"].strip(),
                "end_point": row["end_point"].strip(),
                "priority": row["priority"],
                "remaining_time": int(row["remaining_time"]) if row["remaining_time"] not in [None, "", "None"] else None
            })

    # 地图数据
    start_points, end_points, agv_list = {}, {}, []
    with open(map_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            t, name = row["type"].strip(), row["name"].strip()
            x, y = int(row["x"]), int(row["y"])
            if t == "start_point":
                start_points[name] = (x, y)
            elif t == "end_point":
                end_points[name] = (x, y)
            elif t == "agv":
                agv_list.append({
                    "id": name,
                    "pose": (x, y),
                    "pitch": int(row["pitch"])
                })

    init_csv(agv_list)

    # 构建任务队列
    task_queues = {}
    for task in all_tasks:
        sp = task["start_point"]
        if sp not in task_queues:
            task_queues[sp] = []
        task_queues[sp].append(task)

    # 初始化AGV状态
    agv_count = len(agv_list)
    agv_queue = [agv["id"] for agv in agv_list[:agv_count]]
    agv_states = {
        agv["id"]: {
            "pos": tuple(agv["pose"]),
            "pitch": agv["pitch"],
            "time": 1,
            "home": tuple(agv["pose"]),
            "busy": False,
            "last_task_time": 0
        } for agv in agv_list[:agv_count]
    }

    reservation_table = {}
    assigned_tasks = []

    # 预约AGV初始位置
    for agv in agv_list:
        reservation_table[(0, tuple(agv["pose"]))] = agv["id"]

    iteration_count = 0
    max_iterations = 1000  # 适中的最大迭代次数
    consecutive_no_progress = 0

    print("[INFO] 开始零碰撞任务分配...")

    while any(task_queues[sp] for sp in task_queues) and iteration_count < max_iterations:
        iteration_count += 1
        progress = False

        # 检查是否所有AGV都超过时间限制
        active_agvs = [agv for agv in agv_queue if agv_states[agv]["time"] <= MAX_TIME]
        if not active_agvs:
            print(f"[INFO] 所有AGV都已超过时间限制({MAX_TIME}秒)，停止任务分配")
            break

        # 按照AGV的空闲时间排序，优先使用空闲时间长的AGV
        active_agvs.sort(key=lambda agv: agv_states[agv]["last_task_time"])

        for agv in active_agvs:
            state = agv_states[agv]

            # 跳过忙碌的AGV
            if state["busy"]:
                continue

            # 收集所有可用任务并按优先级排序
            available_tasks = []
            for sp in task_queues.keys():
                if task_queues[sp]:
                    task = task_queues[sp][0]
                    start_coord = get_pickup_coord(sp, start_points[sp])
                    distance = manhattan(state["pos"], start_coord)

                    # 计算任务优先级分数
                    priority_score = 0
                    if task["priority"].lower() == "urgent":
                        if task["remaining_time"] is not None:
                            time_pressure = max(0, task["remaining_time"] - state["time"])
                            priority_score = -1000 + time_pressure  # 越紧急分数越低（优先级越高）
                        else:
                            priority_score = -500
                    else:
                        priority_score = distance  # 普通任务按距离排序

                    available_tasks.append((priority_score, distance, sp, task))

            # 按优先级排序
            available_tasks.sort(key=lambda x: (x[0], x[1]))

            # 尝试分配任务
            for _, _, sp, task in available_tasks:
                if not task_queues[sp]:  # 任务可能已被分配
                    continue

                success = try_assign_task_safely(agv, task, sp, start_points, end_points,
                                               agv_states, reservation_table, scoring_system)

                if success:
                    assigned_tasks.append(success)
                    task_queues[sp].pop(0)
                    progress = True
                    break  # 成功分配一个任务后处理下一个AGV

        # 进度检查
        if not progress:
            consecutive_no_progress += 1
            if consecutive_no_progress >= 100:
                print(f"[WARN] 连续{consecutive_no_progress}次无进展，尝试清理预约表...")
                cleanup_old_reservations(reservation_table, agv_states)
                consecutive_no_progress = 0
        else:
            consecutive_no_progress = 0

        # 定期报告进度
        if iteration_count % 200 == 0:
            remaining_tasks = sum(len(tasks) for tasks in task_queues.values())
            print(f"[INFO] 迭代{iteration_count}: 剩余{remaining_tasks}个任务, 已分配{len(assigned_tasks)}个任务")

    final_time = max(state["time"] for state in agv_states.values())
    finalize_trajectory_csv(agv_states, final_time, scoring_system)

    # 计算并保存评分结果
    score_details = scoring_system.save_scoring_results()

    print(f"[INFO] 零碰撞分配完成！")
    print(f"[INFO] 共分配任务数：{len(assigned_tasks)}")
    print(f"[INFO] 碰撞次数：{score_details['collision_count']}")
    print(f"[INFO] 避免碰撞次数：{score_details['collision_avoidance_count']}")
    print(f"[INFO] 总分：{score_details['total_score']}/120")

    return assigned_tasks, score_details

def try_assign_task_safely(agv, task, sp, start_points, end_points, agv_states, reservation_table, scoring_system):
    """
    安全地尝试分配任务，确保不会产生冲突
    """
    state = agv_states[agv]
    ep = task["end_point"]
    priority = task["priority"]
    emergency = True if priority.lower() == "urgent" else False
    taskid = task["task_id"]
    remaining_time = task["remaining_time"]

    start_coord = get_pickup_coord(sp, start_points[sp])
    end_coord_main = end_points[ep]
    delivery_candidates = get_delivery_options(end_coord_main)

    # 找到最安全的卸货点
    safest_delivery = find_safest_delivery_point(
        start_coord, delivery_candidates, reservation_table, state["time"], agv
    )

    if not safest_delivery:
        return None

    # 重新排序卸货点，优先使用最安全的
    delivery_candidates = [safest_delivery] + [d for d in delivery_candidates if d != safest_delivery]

    # 尝试每个卸货点
    for delivery_point in delivery_candidates:
        # 检查卸货点是否在有效范围内
        if not (1 <= delivery_point[0] <= 20 and 1 <= delivery_point[1] <= 20):
            continue

        # 1. 规划到取料点的路径
        pickup_path, pickup_end_time = enhanced_a_star(
            state["pos"], start_coord,
            set(start_points.values()) | set(end_points.values()),
            reservation_table, state["time"], agv
        )

        if not pickup_path:
            continue

        # 2. 模拟到取料点的路径
        pickup_steps, t1, pitch1, pos1 = safe_simulate_path_strict(
            agv, pickup_path, state["time"], state["pitch"], False, "", False, reservation_table
        )

        if not pickup_steps or t1 > MAX_TIME:
            continue

        # 检查取料路径是否安全
        if not reserve_path_safely(pickup_steps, reservation_table, agv, scoring_system):
            continue

        # 3. 取料步骤
        pickup_action = {
            "timestamp": t1,
            "name": agv,
            "X": pos1[0],
            "Y": pos1[1],
            "pitch": pitch1,
            "loaded": "true",
            "destination": ep,
            "Emergency": emergency,
            "task-id": taskid
        }

        # 检查取料时间点是否安全
        if (t1, (pos1[0], pos1[1])) in reservation_table:
            if reservation_table[(t1, (pos1[0], pos1[1]))] != agv:
                # 回滚已预约的路径
                rollback_reservations(pickup_steps, reservation_table, agv)
                continue

        t1 += 1

        # 4. 规划到卸货点的路径
        delivery_path, delivery_end_time = enhanced_a_star(
            pos1, delivery_point,
            set(start_points.values()) | set(end_points.values()),
            reservation_table, t1, agv
        )

        if not delivery_path:
            rollback_reservations(pickup_steps, reservation_table, agv)
            continue

        # 5. 模拟到卸货点的路径
        delivery_steps, t2, pitch2, pos2 = safe_simulate_path_strict(
            agv, delivery_path, t1, pitch1, True, ep, emergency, reservation_table
        )

        if not delivery_steps or t2 > MAX_TIME:
            rollback_reservations(pickup_steps, reservation_table, agv)
            continue

        # 检查卸货路径是否安全
        if not reserve_path_safely(delivery_steps, reservation_table, agv, scoring_system):
            rollback_reservations(pickup_steps, reservation_table, agv)
            continue

        # 6. 卸货步骤
        delivery_action = {
            "timestamp": t2,
            "name": agv,
            "X": pos2[0],
            "Y": pos2[1],
            "pitch": pitch2,
            "loaded": "false",
            "destination": "",
            "Emergency": "false",
            "task-id": taskid
        }

        # 检查卸货时间点是否安全
        if (t2, (pos2[0], pos2[1])) in reservation_table:
            if reservation_table[(t2, (pos2[0], pos2[1]))] != agv:
                rollback_reservations(pickup_steps + delivery_steps, reservation_table, agv)
                continue

        t2 += 1

        # 7. 所有检查通过，执行任务
        all_steps = pickup_steps + [pickup_action] + delivery_steps + [delivery_action]

        # 预约取料和卸货动作的时间点
        reservation_table[(pickup_action["timestamp"], (pickup_action["X"], pickup_action["Y"]))] = agv
        reservation_table[(delivery_action["timestamp"], (delivery_action["X"], delivery_action["Y"]))] = agv

        # 写入CSV
        append_to_csv(all_steps)

        # 更新AGV状态
        agv_states[agv] = {
            "pos": pos2,
            "pitch": pitch2,
            "time": t2,
            "home": state["home"],
            "busy": False,
            "last_task_time": t2
        }

        # 记录任务完成信息
        task_info = {
            "task_id": taskid,
            "agv": agv,
            "start_point": sp,
            "end_point": ep,
            "priority": priority,
            "remaining_time": remaining_time,
            "completion_time": t2,
            "start_time": state["time"],
            "agv_start_pose": state["pos"],
            "agv_start_orientation": state["pitch"]
        }

        scoring_system.add_completed_task(task_info)

        # 如果是高优先级任务，单独记录
        if emergency and remaining_time is not None:
            scoring_system.add_urgent_task(task_info)

        print(f"[SUCCESS] AGV {agv} 安全完成任务 {taskid}: {sp} -> {ep} (时间: {state['time']}->{t2})")

        return task_info

    return None  # 所有卸货点都尝试失败

def rollback_reservations(steps, reservation_table, agv_name):
    """回滚预约"""
    for step in steps:
        key = (step["timestamp"], (step["X"], step["Y"]))
        if key in reservation_table and reservation_table[key] == agv_name:
            del reservation_table[key]

def cleanup_old_reservations(reservation_table, agv_states):
    """清理过期的预约"""
    current_min_time = min(state["time"] for state in agv_states.values())
    cutoff_time = current_min_time - 50  # 清理50秒前的预约

    keys_to_remove = [key for key in reservation_table.keys() if key[0] < cutoff_time]
    for key in keys_to_remove:
        del reservation_table[key]

    if keys_to_remove:
        print(f"[INFO] 清理了{len(keys_to_remove)}个过期预约")

def to_lower_str(x):
    """转换为小写字符串"""
    return str(x).lower() if pd.notna(x) else x

def finalize_trajectory_csv(agv_states, final_time, scoring_system, csv_path=CSV_PATH):
    """
    完成轨迹CSV文件
    """
    with open(csv_path, 'a', newline='') as f:
        writer = csv.writer(f)
        for agv_id, state in agv_states.items():
            # 如果AGV因碰撞消失，不再补充轨迹
            if scoring_system.agv_status.get(agv_id) == 'disappeared':
                continue

            last_time = state["time"]
            for t in range(last_time, final_time + 1):
                writer.writerow([
                    t,
                    agv_id,
                    state["pos"][0],
                    state["pos"][1],
                    state["pitch"],
                    "false", "", "false"
                ])

    # 排序并保存
    col_names = pd.read_csv(csv_path, nrows=0).columns.tolist()
    df = pd.read_csv(csv_path, converters={col_names[5]: to_lower_str, col_names[7]: to_lower_str})
    df.sort_values(by=["timestamp", "name"], inplace=True)
    df.to_csv(csv_path, index=False)
    print(f"[INFO] 最终AGV轨迹文件已保存到: {csv_path}")

    # 零碰撞检测
    detect_zero_collisions(csv_path=csv_path, output_path=COLLISION_PATH, scoring_system=scoring_system)

def detect_zero_collisions(csv_path, output_path, scoring_system):
    """
    零碰撞检测 - 应该检测不到任何碰撞
    """
    position_states = defaultdict(dict)
    agv_positions = defaultdict(dict)

    # 读取CSV轨迹数据
    with open(csv_path, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            timestamp = int(row['timestamp'])
            x, y = int(row['X']), int(row['Y'])
            agv = row['name']
            position_states[timestamp][(x, y)] = agv
            agv_positions[agv][timestamp] = (x, y)

    collisions = []

    # 静态冲突检测
    for timestamp in position_states:
        pos_counts = defaultdict(list)
        for pos, agv in position_states[timestamp].items():
            pos_counts[pos].append(agv)

        for pos, agvs in pos_counts.items():
            if len(agvs) > 1:
                collision_info = {
                    "timestamp": timestamp,
                    "X": pos[0],
                    "Y": pos[1],
                    "type": "static",
                    "AGVs": ", ".join(agvs)
                }
                collisions.append(collision_info)
                scoring_system.add_collision(collision_info)

    # 对穿冲突检测
    seen_crossings = set()
    for agv1 in agv_positions:
        for agv2 in agv_positions:
            if agv1 >= agv2:
                continue
            for t in agv_positions[agv1]:
                if (t + 1 not in agv_positions[agv1]) or (t not in agv_positions[agv2]) or (t + 1 not in agv_positions[agv2]):
                    continue

                p1_now = agv_positions[agv1][t]
                p1_next = agv_positions[agv1][t + 1]
                p2_now = agv_positions[agv2][t]
                p2_next = agv_positions[agv2][t + 1]

                if p1_now == p2_next and p2_now == p1_next:
                    key = (t, tuple(sorted([agv1, agv2])))
                    if key not in seen_crossings:
                        seen_crossings.add(key)
                        collision_info = {
                            "timestamp": t,
                            "X": p1_now[0],
                            "Y": p1_now[1],
                            "type": "crossing",
                            "AGVs": f"{agv1}, {agv2}"
                        }
                        collisions.append(collision_info)
                        scoring_system.add_collision(collision_info)

    # 保存检测结果
    with open(output_path, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=["timestamp", "X", "Y", "type", "AGVs"])
        writer.writeheader()
        writer.writerows(collisions)

    if collisions:
        print(f"[ERROR] 检测到 {len(collisions)} 个碰撞事件！零碰撞目标未达成。")
    else:
        print(f"[SUCCESS] 🎉 零碰撞目标达成！未检测到任何碰撞事件。")

    print(f"[INFO] 碰撞检测结果已保存到: {output_path}")

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 AGV零碰撞任务分配系统 V3")
    print("=" * 60)

    # 构建数据文件路径
    data_dir = os.path.join(os.path.dirname(__file__), '赛题附件-输入输出格式说明')
    task_file = os.path.join(data_dir, '附件2——task_csv.csv')
    map_file = os.path.join(data_dir, '附件1——map_data.csv')

    start_time = time.time()
    result, score_details = zero_collision_assign_tasks(task_file=task_file, map_file=map_file)
    end_time = time.time()

    print("\n" + "=" * 60)
    print("📊 零碰撞系统评分结果")
    print("=" * 60)
    print(f"✅ 基础分数（正确运达快件）: {score_details['base_score']}")
    print(f"🎯 高优先级任务奖励: +{score_details['urgent_bonus']}")
    print(f"⚠️  高优先级任务惩罚: -{score_details['urgent_penalty']}")
    print(f"💥 碰撞惩罚: -{score_details['collision_penalty']}")
    print(f"🛡️  成功避免碰撞次数: {score_details['collision_avoidance_count']}")
    print(f"🏆 总分: {score_details['total_score']}/120")
    print(f"⏱️  最后任务完成时间: {score_details['last_completion_time']}秒")
    print(f"⚡ 程序运行时间: {end_time - start_time:.2f}秒")

    if score_details['collision_count'] == 0:
        print("\n🎉🎉🎉 恭喜！零碰撞目标达成！🎉🎉🎉")
    else:
        print(f"\n❌ 仍有 {score_details['collision_count']} 次碰撞，需要进一步优化")

    print("=" * 60)
