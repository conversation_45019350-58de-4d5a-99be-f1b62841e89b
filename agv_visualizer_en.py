import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches

def visualize_agv_english():
    """English version of AGV visualization"""
    # Read data
    map_data = pd.read_csv("赛题附件-输入输出格式说明/附件1——map_data.csv")
    trajectory = pd.read_csv("agv_trajectory.csv")
    
    # Parse map data
    start_points = map_data[map_data['type'] == 'start_point']
    end_points = map_data[map_data['type'] == 'end_point']
    agvs = map_data[map_data['type'] == 'agv']
    
    def plot_time_snapshot(t=0):
        """Plot system state at given time"""
        plt.figure(figsize=(12, 10))
        
        # Plot start points (green squares)
        plt.scatter(start_points['x'], start_points['y'], 
                   c='green', s=400, marker='s', alpha=0.8, label='Start Points')
        for _, row in start_points.iterrows():
            plt.text(row['x'], row['y'], row['name'], ha='center', va='center', 
                    fontsize=8, weight='bold', color='white')
        
        # Plot end points (red squares)
        plt.scatter(end_points['x'], end_points['y'], 
                   c='red', s=400, marker='s', alpha=0.8, label='End Points')
        for _, row in end_points.iterrows():
            plt.text(row['x'], row['y'], row['name'], ha='center', va='center', 
                    fontsize=6, weight='bold', color='white')
        
        # Plot AGV current positions
        current_pos = trajectory[trajectory['timestamp'] == t]
        colors = plt.cm.tab10(np.linspace(0, 1, len(current_pos)))
        
        for i, (_, agv) in enumerate(current_pos.iterrows()):
            plt.scatter(agv['X'], agv['Y'], c=[colors[i]], s=300, 
                       marker='o', edgecolor='black', linewidth=2, alpha=0.9)
            plt.text(agv['X'], agv['Y']+0.7, agv['name'], 
                    ha='center', va='center', fontsize=9, weight='bold')
            
            # Status info
            status = f"Loaded: {agv['loaded']}"
            if agv['destination'] and agv['destination'] != '':
                status += f"\\nTarget: {agv['destination']}"
            if agv['Emergency'] == 'true':
                status += "\\n⚠️ URGENT!"
            
            plt.text(agv['X'], agv['Y']-1.2, status, ha='center', va='center', 
                    fontsize=7, bbox=dict(boxstyle="round,pad=0.3", 
                                        facecolor=colors[i], alpha=0.7))
        
        plt.xlim(0, 21)
        plt.ylim(0, 21)
        plt.grid(True, alpha=0.3)
        plt.title(f'AGV System Status - Time: {t}s', fontsize=16, weight='bold')
        plt.xlabel('X Coordinate', fontsize=12)
        plt.ylabel('Y Coordinate', fontsize=12)
        plt.legend()
        plt.tight_layout()
        plt.show()
    
    def plot_trajectory_heatmap():
        """Plot trajectory heatmap"""
        plt.figure(figsize=(12, 10))
        
        # Count visits per position
        visit_count = trajectory.groupby(['X', 'Y']).size().reset_index(name='count')
        
        # Create heatmap
        scatter = plt.scatter(visit_count['X'], visit_count['Y'], 
                           c=visit_count['count'], s=visit_count['count']*3, 
                           cmap='Reds', alpha=0.8)
        plt.colorbar(scatter, label='Visit Count')
        
        # Overlay map info
        plt.scatter(start_points['x'], start_points['y'], 
                   c='green', s=400, marker='s', alpha=0.9, label='Start Points')
        plt.scatter(end_points['x'], end_points['y'], 
                   c='blue', s=400, marker='s', alpha=0.9, label='End Points')
        
        # Add labels
        for _, row in start_points.iterrows():
            plt.text(row['x'], row['y'], row['name'], ha='center', va='center', 
                    fontsize=6, weight='bold', color='white')
        for _, row in end_points.iterrows():
            plt.text(row['x'], row['y'], row['name'], ha='center', va='center', 
                    fontsize=5, weight='bold', color='white')
        
        plt.xlim(0, 21)
        plt.ylim(0, 21)
        plt.grid(True, alpha=0.3)
        plt.title('AGV Trajectory Heatmap', fontsize=16, weight='bold')
        plt.xlabel('X Coordinate', fontsize=12)
        plt.ylabel('Y Coordinate', fontsize=12)
        plt.legend()
        plt.tight_layout()
        plt.show()
    
    def plot_agv_paths():
        """Plot individual AGV paths"""
        plt.figure(figsize=(14, 10))
        
        # Get unique AGVs
        agv_names = trajectory['name'].unique()
        colors = plt.cm.tab10(np.linspace(0, 1, len(agv_names)))
        
        for i, agv_name in enumerate(agv_names):
            agv_data = trajectory[trajectory['name'] == agv_name].sort_values('timestamp')
            plt.plot(agv_data['X'], agv_data['Y'], 
                    color=colors[i], linewidth=2, alpha=0.7, label=agv_name)
            
            # Mark start and end
            plt.scatter(agv_data.iloc[0]['X'], agv_data.iloc[0]['Y'], 
                       color=colors[i], s=100, marker='o', edgecolor='black')
            plt.scatter(agv_data.iloc[-1]['X'], agv_data.iloc[-1]['Y'], 
                       color=colors[i], s=100, marker='*', edgecolor='black')
        
        # Add map elements
        plt.scatter(start_points['x'], start_points['y'], 
                   c='green', s=200, marker='s', alpha=0.5, label='Start')
        plt.scatter(end_points['x'], end_points['y'], 
                   c='red', s=200, marker='s', alpha=0.5, label='End')
        
        plt.xlim(0, 21)
        plt.ylim(0, 21)
        plt.grid(True, alpha=0.3)
        plt.title('AGV Movement Paths', fontsize=16, weight='bold')
        plt.xlabel('X Coordinate', fontsize=12)
        plt.ylabel('Y Coordinate', fontsize=12)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.tight_layout()
        plt.show()
    
    # Generate visualizations
    print("Generating time snapshots...")
    plot_time_snapshot(0)    # Initial state
    plot_time_snapshot(50)   # 50 seconds
    plot_time_snapshot(100)  # 100 seconds
    
    print("Generating trajectory heatmap...")
    plot_trajectory_heatmap()
    
    print("Generating AGV paths...")
    plot_agv_paths()
    
    # Print statistics
    agv_names = trajectory['name'].unique()
    print("\\n=== AGV System Statistics ===")
    print(f"Total AGVs: {len(agv_names)}")
    print(f"Total simulation time: {trajectory['timestamp'].max()} seconds")
    print(f"Total trajectory records: {len(trajectory)}")
    
    # Emergency tasks
    emergency_records = trajectory[trajectory['Emergency'] == 'true']
    print(f"Emergency task records: {len(emergency_records)}")
    
    # Task completion analysis
    loaded_records = trajectory[trajectory['loaded'] == 'true']
    print(f"Records with loaded cargo: {len(loaded_records)}")
    
    # Collision analysis
    try:
        collisions = pd.read_csv("agv_collisions.csv")
        print(f"Total collisions detected: {len(collisions)}")
        if len(collisions) > 0:
            print(f"Collision types: {collisions['type'].value_counts().to_dict()}")
    except FileNotFoundError:
        print("No collision data found")

if __name__ == "__main__":
    visualize_agv_english()
