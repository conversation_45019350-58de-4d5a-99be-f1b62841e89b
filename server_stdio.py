from typing import Any
from mcp.server.fastmcp import FastMCP
import pandas as pd
import json
import csv
import random
import heapq
import sys
import io
from collections import defaultdict
import os

sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
TURN_COST = 1
LOAD_UNLOAD_TIME = 1
GRID_SIZE = (21, 21) # 可用网格大小为X，1-20,Y，1-20

CSV_PATH = os.path.join(os.getcwd(), "agv_trajectory.csv")


def manhattan(p1, p2):
    """
    该参考方法
    用于计算曼哈顿距离
    """
    return abs(p1[0] - p2[0]) + abs(p1[1] - p2[1])

def get_orientation(from_pos, to_pos):
    """
    用于比较朝向，计算转向角度
    """
    dx, dy = to_pos[0] - from_pos[0], to_pos[1] - from_pos[1]
    if dx > 0:
        return 0
    elif dx < 0:
        return 180
    elif dy > 0:
        return 90
    elif dy < 0:
        return 270
    else:
        return None

def a_star(start, goal, obstacles, grid_size=(21, 21)):
    """
    参考的AGV路径规划的A*算法，
    AGV仅可以在X，Y方向移动，代价函数包含取放料时间+转向时间+路程时间
    obstacles可以增加动态的障碍信息，默认为地图输入障碍信息
    """
    def neighbors(pos):
        x, y = pos
        for dx, dy in [(1,0), (-1,0), (0,1), (0,-1)]:
            nx, ny = x + dx, y + dy
            if 1 <= nx <= grid_size[0] and 1 <= ny <= grid_size[1] and (nx, ny) not in obstacles:
                yield (nx, ny)

    frontier = []
    heapq.heappush(frontier, (manhattan(start, goal), 0, start, [start]))
    visited = set()

    while frontier:
        _, cost, current, path = heapq.heappop(frontier)
        if current == goal:
            return path
        if current in visited:
            continue
        visited.add(current)
        for neighbor in neighbors(current):
            if neighbor not in visited:
                heapq.heappush(frontier, (cost + 1 + manhattan(neighbor, goal), cost + 1, neighbor, path + [neighbor]))
    return []
def simulate_path_strict_load(name, path, start_time, initial_pitch, loaded, destination, emergency,taskid):
    """
    参考的AGV从当前位置前往取料点取料的AGV路径模拟方法
    从路径生成每一秒AGV状态，严格满足以下规则：
    - 原地转向需1秒
    - X/Y移动每次1秒，不能同时转向和移动
    - 每条记录代表一个时间戳的AGV状态
    """
    steps = []
    t = start_time
    pitch = initial_pitch
    last = path[0]

    for current in path[1:]:
        new_pitch = get_orientation(last, current)

        # 若朝向变化，则先转向
        if new_pitch != pitch:
            steps.append({
                "timestamp": t,
                "name": name,
                "X": last[0],
                "Y": last[1],
                "pitch": new_pitch,
                "loaded": loaded,
                "destination": destination if loaded else "",
                "Emergency": False,
                "taskid": taskid
            })
            pitch = new_pitch
            t += 1

        # 移动到下一个位置
        steps.append({
            "timestamp": t,
            "name": name,
            "X": current[0],
            "Y": current[1],
            "pitch": pitch,
            "loaded": loaded,
            "destination": destination if loaded else "",
            "Emergency": False,
            "taskid": taskid
        })
        last = current
        t += 1

    steps.append({
        "timestamp": t,
        "name": name,
        "X": current[0],
        "Y": current[1],
        "pitch": pitch,
        "loaded": True,
        "destination": destination,
        "Emergency": emergency,
        "task-id": taskid
    })


    return steps, t, pitch, last

def simulate_path_strict_unload(name, path, start_time, initial_pitch, loaded, destination, emergency,taskid):
    """
    参考的从取料点到卸料点的AGV路径模拟方法
    从路径生成每一秒AGV状态，严格满足以下规则：
    - 原地转向需1秒
    - X/Y移动每次1秒，不能同时转向和移动
    - 每条记录代表一个时间戳的AGV状态
    """
    steps = []
    t = start_time
    pitch = initial_pitch
    last = path[0]

    for current in path[1:]:
        new_pitch = get_orientation(last, current)

        # 若朝向变化，则先转向
        if new_pitch != pitch:
            steps.append({
                "timestamp": t,
                "name": name,
                "X": last[0],
                "Y": last[1],
                "pitch": new_pitch,
                "loaded": loaded,
                "destination": destination if loaded else "",
                "Emergency": emergency,
                "taskid": taskid
            })
            pitch = new_pitch
            t += 1

        # 移动到下一个位置
        steps.append({
            "timestamp": t,
            "name": name,
            "X": current[0],
            "Y": current[1],
            "pitch": pitch,
            "loaded": loaded,
            "destination": destination if loaded else "",
            "Emergency": emergency,
            "taskid": taskid
        })
        last = current
        t += 1

    # steps.append({
    #     "timestamp": t,
    #     "name": name,
    #     "X": current[0],
    #     "Y": current[1],
    #     "pitch": pitch,
    #     "loaded": "true",
    #     "destination": destination if loaded else "",
    #     "Emergency": emergency,
    #     "task-id": taskid

    # ➕ 加入卸货动作
    steps.append({
        "timestamp": t,
        "name": name,
        "X": current[0],
        "Y": current[1],
        "pitch": pitch,
        "loaded": "false",
        "destination": "",
        "Emergency": "false",
        "task-id": taskid
    })


    return steps, t, pitch, last

def reserve_simulated_steps(steps, reservation_table, agv_name):
    """
    把AGV的路径记录写入 reservation_table，键为 (timestamp, (x, y))。
    用于后续路径冲突判断。
    """
    for step in steps:
        key = (step["timestamp"], (step["X"], step["Y"]))
        reservation_table[key] = agv_name


def init_csv(agv_list):
    """
        初始化CSV_PATH的AGV轨迹文件，timestamp=0需要在最终轨迹文件中体现。
        """
    with open(CSV_PATH, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(["timestamp", "name", "X", "Y", "pitch", "loaded", "destination", "Emergency", "task-id"])
        for agv in agv_list:
            writer.writerow([
                0,
                agv["id"],
                agv["pose"][0],
                agv["pose"][1],
                agv["pitch"],
                "false",
                "",
                "false",
                ""
            ])

def append_to_csv(steps):
    with open(CSV_PATH, 'a', newline='') as f:
        writer = csv.writer(f)
        for step in steps:
            writer.writerow([
                step["timestamp"],
                step["name"],
                step["X"],
                step["Y"],
                step["pitch"],
                str(step["loaded"]).lower(),
                step["destination"],
                str(step["Emergency"]).lower(),
                step.get("task-id", "")

            ])

def get_pickup_coord(start_point_name, original_coord):
    """
        起始点与终点为AGV行使的障碍位置。
        "Tiger", "Dragon", "Horse"取料点位置需要是起始任务点X+1的偏置位置
        其余任务起始点取料点位置需要是起始任务点X-1的偏置位置
     """
    if start_point_name in ["Tiger", "Dragon", "Horse"]:
        return (original_coord[0] + 1, original_coord[1])
    else:
        return (original_coord[0] - 1, original_coord[1])

def get_delivery_options(dest_coord):
    """
        终点为AGV行使的障碍位置。
        每个终点卸料点位置共4个可选位置，分别为任务终点坐标(x,y)的 (x+1, y), (x-1, y), (x, y+1), (x, y-1)的偏置位置
    """
    x, y = dest_coord
    return [(x+1, y), (x-1, y), (x, y+1), (x, y-1)]

def is_head_on_swap_conflict(path_steps, reservation_table):
    """
    检查路径中是否存在对穿（head-on swap）冲突
    返回冲突位置集合 {(x, y), ...}
    """
    conflict_points = set()
    for i in range(len(path_steps) - 1):
        now = (path_steps[i]["timestamp"], (path_steps[i]["X"], path_steps[i]["Y"]))
        nxt = (path_steps[i + 1]["timestamp"], (path_steps[i + 1]["X"], path_steps[i + 1]["Y"]))

        for (t, pos), other_agv in reservation_table.items():
            if t == now[0] and pos == nxt[1]:
                back_key = (t + 1, now[1])
                if back_key in reservation_table and reservation_table[back_key] == other_agv:
                    # 对穿冲突位置记录
                    conflict_points.add(now[1])
                    conflict_points.add(nxt[1])
    return conflict_points

def is_conflict(path_steps, reservation_table):
    """
    检测路径步骤中是否有与已有预约路径冲突的情况
    返回值:
        - has_conflict: 是否存在冲突
        - conflict_points: 所有冲突位置的坐标集合 {(x, y), ...}
    """
    conflict_points = set()

    for step in path_steps:
        key = (step["timestamp"], (step["X"], step["Y"]))
        if key in reservation_table:
            conflict_points.add((step["X"], step["Y"]))

    # 检查对穿冲突
    swap_points = is_head_on_swap_conflict(path_steps, reservation_table)
    conflict_points.update(swap_points)

    return (len(conflict_points) > 0), conflict_points


def find_nearest_task_queue(agv_pos, task_queues, pickup_locks, start_points):
    candidates = []
    for sp in task_queues:
        if task_queues[sp] and not pickup_locks[sp]:
            dist = manhattan(agv_pos, start_points[sp])
            candidates.append((dist, sp))
    if not candidates:
        return None
    return sorted(candidates, key=lambda x: x[0])[0][1]
# initialize MCP server
mcp = FastMCP("PathServer")
 
@mcp.tool()
async def calculatePath(agv_position: str, agv_task: str) -> str:
    """
        该示例参考方法
        不考虑A任务队列的紧急任务的处理，以及取料任务序列的优化
        仅按照随机性选择任务，并保证每个子队列任务的顺序不被打乱
    """
    def get_all_obstacles():
        return set(start_points.values()) | set(end_points.values())

    # --- 数据加载 ---
    all_tasks = []
    #current_script_path = os.path.abspath(__file__)
        #print("当前脚本路径:", current_script_path)

        # 获取当前脚本所在的目录
    #current_directory = os.path.dirname(current_script_path)
    #agv_position = os.path.join(current_directory, agv_position)
    #agv_task = os.path.join(current_directory, agv_task)
    agv_position = os.path.join(os.getcwd(), agv_position)
    agv_task = os.path.join(os.getcwd(), agv_task)

    with open(agv_task, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            all_tasks.append({
                "task_id": row["task_id"],
                "start_point": row["start_point"].strip(),
                "end_point": row["end_point"].strip(),
                "priority": row["priority"],
                "remaining_time": int(row["remaining_time"]) if row["remaining_time"] not in [None, "", "None"] else None
            })

    # --- 地图数据 ---
    start_points, end_points, agv_list = {}, {}, []
    with open(agv_position, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            t, name = row["type"].strip(), row["name"].strip()
            x, y = int(row["x"]), int(row["y"])
            if t == "start_point":
                start_points[name] = (x, y)
            elif t == "end_point":
                end_points[name] = (x, y)
            elif t == "agv":
                agv_list.append({
                    "id": name,
                    "pose": (x, y),
                    "pitch": int(row["pitch"])
                })

    init_csv(agv_list)

    # ------------------ 初始化任务和AGV状态 ------------------
    task_queues = {}
    for task in all_tasks:
        sp = task["start_point"]
        if sp not in task_queues:
            task_queues[sp] = []
        task_queues[sp].append(task)

    agv_queue = [agv["id"] for agv in agv_list]
    agv_states = {
        agv["id"]: {
            "pos": tuple(agv["pose"]),
            "pitch": agv["pitch"],
            "time": 1,
            "home": tuple(agv["pose"])
        } for agv in agv_list
    }

    """
        在该参考算法中，由于避障能力较弱，因此只考虑投入有限数量的AGV进行任务处理
        """
    # 可以根据任务情况选择需要调用的AGV数量，此处用最多的AGV
    retain_count = 12
    # 更新队列
    agv_queue = agv_queue[:retain_count]

    reservation_table = {}
    assigned_tasks = []

    pickup_locks = {sp: False for sp in task_queues}
    pickup_release_time = {sp: -1 for sp in task_queues}
    global_time = 1

    while any(task_queues[sp] for sp in task_queues):
        # ✨ 解锁机制
        for sp in pickup_locks:
            if pickup_locks[sp] and global_time > pickup_release_time[sp]:
                pickup_locks[sp] = False

        agv_queue.sort(key=lambda aid: agv_states[aid]["time"])  # 按空闲时间升序调度
        agv_progress = {aid: False for aid in agv_queue}

        for agv in agv_queue:
            state = agv_states[agv]
            if state["time"] > global_time:
                continue

            # 选择最近未锁队列
            sp = find_nearest_task_queue(state["pos"], task_queues, pickup_locks, start_points)
            if not sp:
                continue

            task = task_queues[sp][0]  # FIFO任务
            ep = task["end_point"]
            priority = task["priority"]
            emergency = True if priority.lower() == "urgent" else False
            taskid = task["task_id"]

            start_coord = get_pickup_coord(sp, start_points[sp])
            end_coord_main = end_points[ep]
            delivery_candidates = get_delivery_options(end_coord_main)
            # 获取当前路径动态故障点位，主要为AGV停留位置，提供给路径规划算法
            dynamic_obstacles = {
                agv_states[other_agv]["pos"]
                for other_agv in agv_queue
                if other_agv != agv and agv_states[other_agv]["time"] <= global_time+40 #最长距离约40
            }

            path_to_pick = a_star(state["pos"], start_coord, dynamic_obstacles|set(start_points.values()) | set(end_points.values()))
            if not path_to_pick:
                continue

            steps1, t1, pitch1, pos1 = simulate_path_strict_load(agv, path_to_pick, max(state["time"],global_time), state["pitch"], False, ep, emergency, taskid)
            has_conflict, conflict_points = is_conflict(steps1, reservation_table)

            if has_conflict: #再次尝试新路径
                path_to_pick = a_star(state["pos"], start_coord, conflict_points|dynamic_obstacles| set(start_points.values()) | set(end_points.values()))
                if not path_to_pick:
                    continue
                steps1, t1, pitch1, pos1 = simulate_path_strict_load(agv, path_to_pick, max(state["time"], global_time),state["pitch"], False, ep, emergency, taskid)
                has_conflict, conflict_points = is_conflict(steps1, reservation_table)
                if has_conflict: # 再次尝试失败则跳过
                    continue
            if any((step["timestamp"], (step["X"], step["Y"])) in reservation_table for step in steps1):
                continue
            t1 += 1

            # 找可行交付路径（任一可行即可）
            best_steps2, best_t2, best_pitch2, best_pos2 = None, float("inf"), None, None
            for d in delivery_candidates:
                path_to_deliver = a_star(pos1, d, dynamic_obstacles|set(start_points.values()) | set(end_points.values()))
                if not path_to_deliver:
                    continue
                steps2, t2, pitch2, pos2 = simulate_path_strict_unload(agv, path_to_deliver, t1, pitch1, True, ep, emergency, taskid)

                has_conflict, conflict_points = is_conflict(steps2, reservation_table)
                if has_conflict:  # 再次尝试新路径
                    path_to_deliver = a_star(pos1, d, conflict_points |dynamic_obstacles| set(start_points.values()) | set(end_points.values()))
                    if not path_to_deliver:
                        continue
                    steps2, t2, pitch2, pos2 = simulate_path_strict_unload(agv, path_to_deliver, t1, pitch1, True, ep,
                                                                    emergency, taskid)
                    has_conflict, conflict_points = is_conflict(steps2, reservation_table)
                    if has_conflict:# 再次尝试失败则跳过
                        continue
                # if is_conflict(steps2, reservation_table):
                #     continue
                if any((step["timestamp"], (step["X"], step["Y"])) in reservation_table for step in steps2):
                    continue
                best_steps2, best_t2, best_pitch2, best_pos2 = steps2, t2, pitch2, pos2
                break  # 找到一个可用路径就可跳出

            if not best_steps2:
                continue

            best_t2 += 1
            full_steps = steps1 + best_steps2
            reserve_simulated_steps(full_steps, reservation_table, agv)
            append_to_csv(full_steps)
            assigned_tasks.append({
                "agv": agv,
                "start_point": sp,
                "end_point": ep,
                "priority": priority,
                "start_time": max(state["time"],global_time),
                "agv_start_pose": state["pos"],
                "agv_start_orientation": state["pitch"]
            })

            agv_states[agv] = {
                "pos": best_pos2,
                "pitch": best_pitch2,
                "time": best_t2,
                "home": state["home"]
            }

            # 设置锁与解锁时间
            pickup_locks[sp] = True
            pickup_release_time[sp] = t1  # 取货动作时间
            task_queues[sp].pop(0)
            agv_progress[agv] = True

        # 当前时刻无任务，则补充轨迹
        if False in agv_progress.values():
            for agv in agv_queue:
                state = agv_states[agv]
                if state["time"] <= global_time and not agv_progress[agv]:
                    state["time"] += 1
                    idle_steps = []
                    idle_steps.append({
                        "timestamp": global_time,
                        "name": agv,
                        "X": agv_states[agv]["pos"][0],
                        "Y": agv_states[agv]["pos"][1],
                        "pitch": agv_states[agv]["pitch"],
                        "loaded": "false",
                        "destination": "",
                        "Emergency": "false",
                        "task-id": ""
                    })
                    if any((step["timestamp"], (step["X"], step["Y"])) in reservation_table for step in
                           idle_steps):  # 判断是否有可能被规划路径占用
                        continue
                    fill_idle_steps(global_time, global_time, agv_states[agv], agv)
                    reserve_simulated_steps(idle_steps, reservation_table, agv)

        # 全局时间推进
        if not any(agv_progress.values()):
            global_time += 1


    final_time = max(state["time"] for state in agv_states.values())
    finalize_trajectory_csv(agv_states, final_time)
    print(f"[INFO] 分配完成任务共计：{len(assigned_tasks)}，总时长：{final_time} 秒")
    return CSV_PATH

def fill_idle_steps(start_time, end_time, agv_state, agv_name):
    """补足AGV在空闲期间的轨迹，确保轨迹完整"""
    append_to_csv([{
        "timestamp": start_time,
        "name": agv_name,
        "X": agv_state["pos"][0],
        "Y": agv_state["pos"][1],
        "pitch": agv_state["pitch"],
        "loaded": "false",
        "destination": "",
        "Emergency": "false",
        "task-id": ""
    }])


# 定义转换函数
def to_lower_str(x):
    return str(x).lower() if pd.notna(x) else x

def finalize_trajectory_csv(agv_states, final_time, csv_path=CSV_PATH):
    """
    对所有 AGV 的轨迹进行时间补全并排序输出。
    需要输出所有AGV从0时刻到最后一个任务结束时刻的所有AGV轨迹信息，即使AGV原地不动
    若在过程中未补充，需要在最终补全数据
    """

    with open(csv_path, 'a', newline='') as f:
        writer = csv.writer(f)
        for agv_id, state in agv_states.items():
            last_time = state["time"]
            for t in range(last_time, final_time + 1):
                writer.writerow([
                    t,
                    agv_id,
                    state["pos"][0],
                    state["pos"][1],
                    state["pitch"],
                    "false", "", "false",""
                ])

    # 排序逻辑,最终输出的loaded、Emergency对应的列中“true","false"必须保持为小写，否则会影响评分
    # 读取CSV时直接转换
    col_names = pd.read_csv(csv_path, nrows=0).columns.tolist()
    df =pd.read_csv(csv_path,converters={col_names[5]: to_lower_str, col_names[7]: to_lower_str})
    df.sort_values(by=["timestamp", "name"], inplace=True)
    df.to_csv(csv_path, index=False)
    print(f"[INFO] Final all AGV trajectory file with timestamp: saved to {csv_path}")

    # 基础碰撞类型检测方法
    #detect_collisions(csv_path='agv_trajectory.csv', output_path='agv_collisions.csv')



def detect_collisions(csv_path, output_path):
    """
        该示例参考方法
        提供对最终输出的AGV轨迹CSV文件进行轨迹冲突判断，该部分判断可能存在不完善的方面，仅供参考
    """
    position_states = defaultdict(dict)  # {timestamp: {(x, y): agv_name}}
    agv_positions = defaultdict(dict)  # {agv: {timestamp: (x,y)}}

    # 读取CSV轨迹数据
    with open(csv_path, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            timestamp = int(row['timestamp'])
            x, y = int(row['X']), int(row['Y'])
            agv = row['name']
            position_states[timestamp][(x, y)] = agv
            agv_positions[agv][timestamp] = (x, y)

    collisions = []

    # ---- 静态冲突 ----
    for timestamp in position_states:
        pos_counts = defaultdict(list)
        for pos, agv in position_states[timestamp].items():
            pos_counts[pos].append(agv)

        for pos, agvs in pos_counts.items():
            if len(agvs) > 1:
                collisions.append({
                    "timestamp": timestamp,
                    "X": pos[0],
                    "Y": pos[1],
                    "type": "static",
                    "AGVs": ", ".join(agvs)
                })

    # ---- 对穿冲突（去重）----
    seen_crossings = set()
    for agv1 in agv_positions:
        for agv2 in agv_positions:
            if agv1 >= agv2:
                continue  # 避免重复和自身
            for t in agv_positions[agv1]:
                if (t + 1 not in agv_positions[agv1]) or (t + 1 not in agv_positions[agv2]):
                    continue

                p1_now = agv_positions[agv1][t]
                p1_next = agv_positions[agv1][t + 1]
                p2_now = agv_positions[agv2][t]
                p2_next = agv_positions[agv2][t + 1]

                if p1_now == p2_next and p2_now == p1_next:
                    key = (t, tuple(sorted([agv1, agv2])))
                    if key not in seen_crossings:
                        seen_crossings.add(key)
                        collisions.append({
                            "timestamp": t,
                            "X": p1_now[0],
                            "Y": p1_now[1],
                            "type": "crossing",
                            "AGVs": f"{agv1}, {agv2}"
                        })

    # ---- 保存检测结果 ----
    print(">>>>>>>>Collision record start：")
    with open(output_path, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=["timestamp", "X", "Y", "type", "AGVs"])
        writer.writeheader()
        writer.writerows(collisions)
        print(collisions)
    print(">>>>>>>>Collision record end.")

    print(f"[INFO] Detected {len(collisions)} collision events. Saved to {output_path}")
    #return  csv_path



if __name__ == "__main__":
    try:
        mcp.run(transport='stdio')
    except Exception as e:
        print(f"Server crashed: {e}", file=sys.stderr)
        sys.exit(1)