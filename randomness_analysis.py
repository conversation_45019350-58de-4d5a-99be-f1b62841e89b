import json
import csv
import random
import pandas as pd
import heapq
from collections import defaultdict
import os

# 从原始test_runner.py导入所有必要的函数
from test_runner import *

def analyze_randomness_impact():
    """分析随机性对结果的影响"""
    results = []
    
    # 运行多次实验，每次使用不同的随机种子
    for seed in range(10):
        random.seed(seed)
        print(f"\\n=== 运行 #{seed+1} (种子: {seed}) ===")
        
        # 构建文件路径
        data_dir = os.path.join(os.path.dirname(__file__), '赛题附件-输入输出格式说明')
        task_file = os.path.join(data_dir, '附件2——task_csv.csv')
        map_file = os.path.join(data_dir, '附件1——map_data.csv')
        
        # 运行调度算法
        assigned_tasks = assign_tasks(task_file=task_file, map_file=map_file)
        
        # 读取结果
        try:
            collisions = pd.read_csv("agv_collisions.csv")
            collision_count = len(collisions)
        except:
            collision_count = 0
        
        try:
            trajectory = pd.read_csv("agv_trajectory.csv")
            completion_time = trajectory['timestamp'].max()
        except:
            completion_time = 0
        
        results.append({
            'seed': seed,
            'tasks_assigned': len(assigned_tasks),
            'completion_time': completion_time,
            'collision_count': collision_count
        })
        
        print(f"任务分配数: {len(assigned_tasks)}")
        print(f"完成时间: {completion_time}s")
        print(f"碰撞次数: {collision_count}")
    
    return results

def compare_strategies():
    """比较不同的调度策略"""
    
    print("\\n" + "="*50)
    print("📊 调度策略随机性分析")
    print("="*50)
    
    results = analyze_randomness_impact()
    
    # 统计分析
    completion_times = [r['completion_time'] for r in results]
    collision_counts = [r['collision_count'] for r in results]
    
    print("\\n📈 统计结果:")
    print(f"完成时间 - 最小: {min(completion_times)}s, 最大: {max(completion_times)}s")
    print(f"完成时间 - 平均: {sum(completion_times)/len(completion_times):.1f}s")
    print(f"完成时间 - 标准差: {(sum([(x-sum(completion_times)/len(completion_times))**2 for x in completion_times])/len(completion_times))**0.5:.1f}s")
    
    print(f"\\n💥 碰撞次数 - 最小: {min(collision_counts)}, 最大: {max(collision_counts)}")
    print(f"碰撞次数 - 平均: {sum(collision_counts)/len(collision_counts):.1f}")
    print(f"碰撞次数 - 标准差: {(sum([(x-sum(collision_counts)/len(collision_counts))**2 for x in collision_counts])/len(collision_counts))**0.5:.1f}")
    
    # 找出最佳种子
    best_seed = min(results, key=lambda x: (x['completion_time'], x['collision_count']))
    worst_seed = max(results, key=lambda x: (x['completion_time'], x['collision_count']))
    
    print(f"\\n🏆 最佳种子: {best_seed['seed']} (完成时间: {best_seed['completion_time']}s, 碰撞: {best_seed['collision_count']})")
    print(f"🔴 最差种子: {worst_seed['seed']} (完成时间: {worst_seed['completion_time']}s, 碰撞: {worst_seed['collision_count']})")
    
    # 性能变化幅度
    time_variance = (max(completion_times) - min(completion_times)) / min(completion_times) * 100
    collision_variance = (max(collision_counts) - min(collision_counts)) / min(collision_counts) * 100
    
    print(f"\\n📊 性能变化幅度:")
    print(f"完成时间变化: {time_variance:.1f}%")
    print(f"碰撞次数变化: {collision_variance:.1f}%")
    
    return results

def deterministic_strategy():
    """确定性调度策略"""
    print("\\n" + "="*50)
    print("🎯 确定性调度策略 (无随机性)")
    print("="*50)
    
    # 修改assign_tasks函数中的随机部分
    # 将random.shuffle替换为确定性排序
    
    # 可以按照以下策略排序：
    # 1. 按起始点名称字母顺序
    # 2. 按起始点到AGV的距离
    # 3. 按任务优先级
    
    print("建议的确定性策略:")
    print("1. 按起始点名称排序: Tiger -> Dragon -> Horse -> Rabbit -> Ox -> Monkey")
    print("2. 按距离最近原则分配任务")
    print("3. 紧急任务优先处理")

if __name__ == "__main__":
    # 运行分析
    results = compare_strategies()
    
    # 提供确定性策略建议
    deterministic_strategy()
    
    print("\\n" + "="*50)
    print("💡 建议:")
    print("1. 如需要确定性结果，请在代码开头添加 random.seed(42)")
    print("2. 如需要探索不同方案，可以尝试不同的种子值")
    print("3. 考虑将随机策略替换为确定性策略以获得稳定结果")
    print("="*50)
