import json
import csv
import random
import pandas as pd
import heapq
from collections import defaultdict
import os
import time

# V5版本：完整单向道路网络系统 - 彻底解决对穿冲突
TURN_COST = 1
LOAD_UNLOAD_TIME = 1
GRID_SIZE = (21, 21)
CSV_PATH = "agv_trajectory_v5_complete_roads.csv"
COLLISION_PATH = "agv_collisions_v5_complete_roads.csv"
SCORING_PATH = "scoring_results_v5_complete_roads.csv"

# 评分系统常量
MAX_TIME = 300
BASE_SCORE = 100
BONUS_SCORE = 20
URGENT_BONUS = 10
URGENT_PENALTY = 5
COLLISION_PENALTY = 10

random.seed(42)

class CompleteRoadScoringSystem:
    """完整道路网络评分系统"""
    
    def __init__(self):
        self.completed_tasks = []
        self.urgent_tasks = []
        self.collisions = []
        self.agv_status = {}
        self.last_task_completion_time = 0
        self.road_violations = 0
        self.successful_avoidance = 0
        
    def add_road_violation(self):
        self.road_violations += 1
        
    def add_successful_avoidance(self):
        self.successful_avoidance += 1
        
    def add_completed_task(self, task_info):
        self.completed_tasks.append(task_info)
        if task_info.get('completion_time', 0) > self.last_task_completion_time:
            self.last_task_completion_time = task_info.get('completion_time', 0)
            
    def add_urgent_task(self, task_info):
        self.urgent_tasks.append(task_info)
        
    def add_collision(self, collision_info):
        self.collisions.append(collision_info)
        if 'AGVs' in collision_info:
            agvs = collision_info['AGVs'].split(', ')
            for agv in agvs:
                self.agv_status[agv] = 'disappeared'
                
    def calculate_score(self):
        score_details = {
            'base_score': len([t for t in self.completed_tasks if t.get('completion_time', 0) <= MAX_TIME]),
            'urgent_bonus': 0,
            'urgent_penalty': 0,
            'collision_penalty': len(self.collisions) * COLLISION_PENALTY,
            'collision_count': len(self.collisions),
            'road_violations': self.road_violations,
            'successful_avoidance': self.successful_avoidance,
            'last_completion_time': self.last_task_completion_time
        }
        
        # 处理紧急任务
        for urgent_task in self.urgent_tasks:
            remaining_time = urgent_task.get('remaining_time')
            completion_time = urgent_task.get('completion_time')
            
            if remaining_time is not None and completion_time is not None:
                if completion_time <= remaining_time:
                    score_details['urgent_bonus'] += URGENT_BONUS
                else:
                    score_details['urgent_penalty'] += URGENT_PENALTY
        
        score_details['total_score'] = (
            score_details['base_score'] + 
            score_details['urgent_bonus'] - 
            score_details['urgent_penalty'] - 
            score_details['collision_penalty']
        )
        
        return score_details
        
    def save_scoring_results(self, filepath=SCORING_PATH):
        score_details = self.calculate_score()
        
        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['评分项目', '数值', '说明'])
            writer.writerow(['基础分数', score_details['base_score'], f"正确运达快件数量"])
            writer.writerow(['高优先级奖励', score_details['urgent_bonus'], f"按时完成高优先级任务"])
            writer.writerow(['高优先级惩罚', -score_details['urgent_penalty'], f"未按时完成高优先级任务"])
            writer.writerow(['碰撞惩罚', -score_details['collision_penalty'], f"碰撞次数: {score_details['collision_count']}"])
            writer.writerow(['总分', score_details['total_score'], f"满分120分"])
            writer.writerow(['最后完成时间', score_details['last_completion_time'], '用于平分时比较'])
            writer.writerow(['道路违规次数', score_details['road_violations'], '违反道路规则次数'])
            writer.writerow(['成功避让次数', score_details['successful_avoidance'], '成功避让冲突次数'])
            
        print(f"[INFO] 评分结果已保存到: {filepath}")
        print(f"[INFO] 总分: {score_details['total_score']}/120")
        print(f"[INFO] 碰撞次数: {score_details['collision_count']}")
        print(f"[INFO] 道路违规次数: {score_details['road_violations']}")
        print(f"[INFO] 成功避让次数: {score_details['successful_avoidance']}")
        
        return score_details

class CompleteRoadNetwork:
    """完整的单向道路网络系统"""
    
    def __init__(self):
        self.road_directions = self._build_complete_road_network()
        self.valid_positions = set(self.road_directions.keys())
        self._add_terminal_positions()
        
    def _build_complete_road_network(self):
        """构建完整的单向道路网络"""
        directions = {}
        
        # 外环单向循环系统
        # 左侧道路：向上 (x=2)
        for y in range(2, 19):
            directions[(2, y)] = [(0, 1)]  # 只能向上
            
        # 上侧道路：向右 (y=18)
        for x in range(2, 19):
            directions[(x, 18)] = [(1, 0)]  # 只能向右
            
        # 右侧道路：向下 (x=18)
        for y in range(2, 19):
            directions[(18, y)] = [(0, -1)]  # 只能向下
            
        # 下侧道路：向左 (y=2)
        for x in range(2, 19):
            directions[(x, 2)] = [(-1, 0)]  # 只能向左
        
        # 内部网格单向系统 - 交替方向避免对穿
        # 水平道路：奇数行向右，偶数行向左
        for y in [4, 6, 8, 10, 12, 14, 16]:
            for x in range(3, 18):
                if y % 4 == 0:  # y=4,8,12,16 向右
                    directions[(x, y)] = [(1, 0)]
                else:  # y=6,10,14 向左
                    directions[(x, y)] = [(-1, 0)]
        
        # 垂直道路：奇数列向上，偶数列向下
        for x in [4, 6, 8, 10, 12, 14, 16]:
            for y in range(3, 18):
                if x % 4 == 0:  # x=4,8,12,16 向上
                    if (x, y) not in directions:
                        directions[(x, y)] = [(0, 1)]
                else:  # x=6,10,14 向下
                    if (x, y) not in directions:
                        directions[(x, y)] = [(0, -1)]
        
        # 连接道路 - 允许转向的特殊节点
        connection_nodes = [
            # 外环转角
            (2, 2), (2, 18), (18, 18), (18, 2),
            # 内外环连接点
            (3, 3), (3, 17), (17, 17), (17, 3),
            # 内部网格连接点
            (4, 4), (4, 8), (4, 12), (4, 16),
            (8, 4), (8, 8), (8, 12), (8, 16),
            (12, 4), (12, 8), (12, 12), (12, 16),
            (16, 4), (16, 8), (16, 12), (16, 16),
        ]
        
        for x, y in connection_nodes:
            # 连接节点允许多方向，但有优先级
            if (x, y) not in directions:
                directions[(x, y)] = [(1, 0), (0, 1), (-1, 0), (0, -1)]
        
        # AGV初始位置连接
        agv_connections = [
            (3, 1), (6, 1), (9, 1), (12, 1), (15, 1), (18, 1),  # 下方
            (3, 20), (6, 20), (9, 20), (12, 20), (15, 20), (18, 20),  # 上方
        ]
        
        for x, y in agv_connections:
            directions[(x, y)] = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # 初始位置全方向
        
        return directions
    
    def _add_terminal_positions(self):
        """添加终点和起始点位置"""
        # 终点位置
        terminals = [
            (6, 4), (6, 8), (6, 12), (6, 16),    # Beijing, Shanghai, Suzhou, Hangzhou
            (9, 4), (9, 8), (9, 12), (9, 16),    # Nanjing, Wuhan, Changsha, Guangzhou  
            (12, 4), (12, 8), (12, 12), (12, 16), # Chengdu, Xiamen, Kunming, Urumqi
            (15, 4), (15, 8), (15, 12), (15, 16), # Shenzhen, Dalian, Tianjin, Chongqing
        ]
        
        # 起始点位置
        start_points = [
            (1, 6), (1, 10), (1, 14),    # Tiger, Dragon, Horse
            (20, 6), (20, 10), (20, 14)  # Rabbit, Ox, Monkey
        ]
        
        # 添加到有效位置
        self.valid_positions.update(terminals)
        self.valid_positions.update(start_points)
        
        # 为终点和起始点添加连接道路
        for pos in terminals + start_points:
            if pos not in self.road_directions:
                self.road_directions[pos] = [(1, 0), (-1, 0), (0, 1), (0, -1)]
    
    def get_valid_moves(self, pos):
        """获取从当前位置的有效移动方向"""
        return self.road_directions.get(pos, [])
    
    def is_valid_move(self, from_pos, to_pos):
        """检查移动是否符合道路规则"""
        if from_pos not in self.valid_positions or to_pos not in self.valid_positions:
            return False
            
        dx = to_pos[0] - from_pos[0]
        dy = to_pos[1] - from_pos[1]
        
        # 只允许相邻位置移动
        if abs(dx) + abs(dy) != 1:
            return False
            
        valid_directions = self.get_valid_moves(from_pos)
        return (dx, dy) in valid_directions

def manhattan(p1, p2):
    return abs(p1[0] - p2[0]) + abs(p1[1] - p2[1])

def get_orientation(from_pos, to_pos):
    dx, dy = to_pos[0] - from_pos[0], to_pos[1] - from_pos[1]
    if dx > 0:
        return 0
    elif dx < 0:
        return 180
    elif dy > 0:
        return 90
    elif dy < 0:
        return 270
    else:
        return None

def complete_road_a_star(start, goal, road_network, max_iterations=10000):
    """基于完整道路网络的A*算法"""
    def get_neighbors(pos):
        neighbors = []
        valid_moves = road_network.get_valid_moves(pos)
        
        for dx, dy in valid_moves:
            new_pos = (pos[0] + dx, pos[1] + dy)
            if new_pos in road_network.valid_positions:
                neighbors.append(new_pos)
        
        return neighbors

    frontier = []
    heapq.heappush(frontier, (manhattan(start, goal), 0, start, [start]))
    visited = set()
    iterations = 0

    while frontier and iterations < max_iterations:
        iterations += 1
        _, cost, current, path = heapq.heappop(frontier)
        
        if current == goal:
            return path
            
        if current in visited:
            continue
        visited.add(current)
        
        for neighbor in get_neighbors(current):
            if neighbor not in visited:
                new_cost = cost + 1
                # 增加启发式权重，优先选择接近目标的路径
                heuristic = manhattan(neighbor, goal)
                priority = new_cost + heuristic * 1.2  # 增加启发式权重
                heapq.heappush(frontier, (priority, new_cost, neighbor, path + [neighbor]))
    
    return []  # 无法找到路径

def complete_road_simulate_path(name, path, start_time, initial_pitch, loaded, destination, emergency):
    """基于完整道路网络的路径模拟"""
    if not path or len(path) < 2:
        return [], start_time, initial_pitch, path[0] if path else None
        
    steps = []
    t = start_time
    pitch = initial_pitch
    last = path[0]

    for current in path[1:]:
        new_pitch = get_orientation(last, current)
        if new_pitch != pitch and new_pitch is not None:
            steps.append({
                "timestamp": t,
                "name": name,
                "X": last[0],
                "Y": last[1],
                "pitch": new_pitch,
                "loaded": loaded,
                "destination": destination if loaded else "",
                "Emergency": emergency
            })
            pitch = new_pitch
            t += 1

        steps.append({
            "timestamp": t,
            "name": name,
            "X": current[0],
            "Y": current[1],
            "pitch": pitch,
            "loaded": loaded,
            "destination": destination if loaded else "",
            "Emergency": emergency
        })
        last = current
        t += 1

    return steps, t, pitch, last

def complete_road_conflict_check(path_steps, reservation_table, agv_name, scoring_system):
    """完整道路网络的冲突检测"""
    for step in path_steps:
        timestamp = step["timestamp"]
        pos = (step["X"], step["Y"])
        
        # 检查位置冲突
        if (timestamp, pos) in reservation_table:
            if reservation_table[(timestamp, pos)] != agv_name:
                scoring_system.add_successful_avoidance()
                return True
    
    return False

def get_pickup_coord(start_point_name, original_coord):
    """获取取料坐标"""
    if start_point_name in ["Tiger", "Dragon", "Horse"]:
        return (original_coord[0] + 1, original_coord[1])
    else:
        return (original_coord[0] - 1, original_coord[1])

def get_delivery_options(dest_coord):
    """获取卸货选项"""
    x, y = dest_coord
    candidates = [(x+1, y), (x-1, y), (x, y+1), (x, y-1)]
    return [(x, y) for x, y in candidates if 1 <= x <= 20 and 1 <= y <= 20]

def init_csv(agv_list):
    with open(CSV_PATH, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(["timestamp", "name", "X", "Y", "pitch", "loaded", "destination", "Emergency"])
        for agv in agv_list:
            writer.writerow([0, agv["id"], agv["pose"][0], agv["pose"][1], agv["pitch"], "false", "", "false"])

def append_to_csv(steps):
    with open(CSV_PATH, 'a', newline='') as f:
        writer = csv.writer(f)
        for step in steps:
            writer.writerow([
                step["timestamp"], step["name"], step["X"], step["Y"], step["pitch"],
                str(step["loaded"]).lower(), step["destination"], str(step["Emergency"]).lower()
            ])

def complete_road_assign_tasks(task_file, map_file):
    """基于完整道路网络的任务分配"""
    scoring_system = CompleteRoadScoringSystem()
    road_network = CompleteRoadNetwork()

    print(f"[INFO] 完整道路网络包含 {len(road_network.valid_positions)} 个有效位置")

    # 读取任务
    all_tasks = []
    with open(task_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            all_tasks.append({
                "task_id": row["task_id"],
                "start_point": row["start_point"].strip(),
                "end_point": row["end_point"].strip(),
                "priority": row["priority"],
                "remaining_time": int(row["remaining_time"]) if row["remaining_time"] not in [None, "", "None"] else None
            })

    # 读取地图
    start_points, end_points, agv_list = {}, {}, []
    with open(map_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            t, name = row["type"].strip(), row["name"].strip()
            x, y = int(row["x"]), int(row["y"])
            if t == "start_point":
                start_points[name] = (x, y)
            elif t == "end_point":
                end_points[name] = (x, y)
            elif t == "agv":
                agv_list.append({"id": name, "pose": (x, y), "pitch": int(row["pitch"])})

    init_csv(agv_list)

    # 构建任务队列
    task_queues = {}
    for task in all_tasks:
        sp = task["start_point"]
        if sp not in task_queues:
            task_queues[sp] = []
        task_queues[sp].append(task)

    # 初始化AGV状态
    agv_states = {
        agv["id"]: {
            "pos": tuple(agv["pose"]),
            "pitch": agv["pitch"],
            "time": 1,
            "home": tuple(agv["pose"])
        } for agv in agv_list
    }

    reservation_table = {}
    assigned_tasks = []

    # 预约AGV初始位置
    for agv in agv_list:
        reservation_table[(0, tuple(agv["pose"]))] = agv["id"]

    iteration_count = 0
    max_iterations = 600  # 适中的迭代次数
    consecutive_failures = 0

    print("[INFO] 开始完整道路网络的任务分配...")

    while any(task_queues[sp] for sp in task_queues) and iteration_count < max_iterations:
        iteration_count += 1
        progress = False

        # 检查时间限制
        active_agvs = [agv for agv in agv_states.keys() if agv_states[agv]["time"] <= MAX_TIME]
        if not active_agvs:
            print(f"[INFO] 所有AGV都已超过时间限制({MAX_TIME}秒)")
            break

        # 按时间和位置排序AGV，优化调度顺序
        active_agvs.sort(key=lambda agv: (agv_states[agv]["time"], agv_states[agv]["pos"]))

        for agv in active_agvs:
            state = agv_states[agv]

            # 收集可用任务并按优先级排序
            available_tasks = []
            for sp in task_queues.keys():
                if task_queues[sp]:
                    task = task_queues[sp][0]
                    start_coord = get_pickup_coord(sp, start_points[sp])

                    # 检查起始坐标是否在道路网络中
                    if start_coord not in road_network.valid_positions:
                        continue

                    # 使用道路网络计算实际路径长度
                    test_path = complete_road_a_star(state["pos"], start_coord, road_network, max_iterations=1000)
                    if not test_path:
                        continue

                    path_length = len(test_path)

                    # 优先级计算：紧急任务优先，然后按路径长度
                    if task["priority"].lower() == "urgent":
                        priority_score = -1000 + path_length  # 紧急任务优先
                    else:
                        priority_score = path_length

                    available_tasks.append((priority_score, sp, task, path_length))

            # 按优先级排序
            available_tasks.sort(key=lambda x: x[0])

            # 尝试分配任务
            for _, sp, task, _ in available_tasks:
                if not task_queues[sp]:
                    continue

                success = try_assign_task_complete_road(agv, task, sp, start_points, end_points,
                                                      agv_states, reservation_table, scoring_system, road_network)

                if success:
                    assigned_tasks.append(success)
                    task_queues[sp].pop(0)
                    progress = True
                    consecutive_failures = 0
                    break

        # 进度管理
        if not progress:
            consecutive_failures += 1
            if consecutive_failures >= 50:
                print(f"[WARN] 连续{consecutive_failures}次无进展，清理预约表...")
                cleanup_old_reservations_complete_road(reservation_table, agv_states)
                consecutive_failures = 0

        # 进度报告
        if iteration_count % 100 == 0:
            remaining_tasks = sum(len(tasks) for tasks in task_queues.values())
            print(f"[INFO] 迭代{iteration_count}: 剩余{remaining_tasks}个任务, 已分配{len(assigned_tasks)}个任务")

    # 完成处理
    final_time = max(state["time"] for state in agv_states.values())
    finalize_trajectory_csv_complete_road(agv_states, final_time, scoring_system)

    score_details = scoring_system.save_scoring_results()

    print(f"[INFO] 完整道路网络分配完成！")
    print(f"[INFO] 共分配任务数：{len(assigned_tasks)}")
    print(f"[INFO] 碰撞次数：{score_details['collision_count']}")
    print(f"[INFO] 道路违规次数：{score_details['road_violations']}")
    print(f"[INFO] 成功避让次数：{score_details['successful_avoidance']}")
    print(f"[INFO] 总分：{score_details['total_score']}/120")

    return assigned_tasks, score_details

def try_assign_task_complete_road(agv, task, sp, start_points, end_points, agv_states, reservation_table, scoring_system, road_network):
    """基于完整道路网络的任务分配尝试"""
    state = agv_states[agv]
    ep = task["end_point"]
    priority = task["priority"]
    emergency = True if priority.lower() == "urgent" else False
    taskid = task["task_id"]
    remaining_time = task["remaining_time"]

    start_coord = get_pickup_coord(sp, start_points[sp])
    end_coord_main = end_points[ep]
    delivery_candidates = get_delivery_options(end_coord_main)

    # 过滤有效的卸货点
    valid_delivery_candidates = [d for d in delivery_candidates if d in road_network.valid_positions]
    if not valid_delivery_candidates:
        return None

    # 按距离排序卸货点
    valid_delivery_candidates.sort(key=lambda d: manhattan(start_coord, d))

    # 尝试每个卸货点
    for delivery_point in valid_delivery_candidates:
        # 1. 到取料点的路径
        pickup_path = complete_road_a_star(state["pos"], start_coord, road_network)
        if not pickup_path:
            continue

        pickup_steps, t1, pitch1, pos1 = complete_road_simulate_path(
            agv, pickup_path, state["time"], state["pitch"], False, "", False
        )

        if not pickup_steps or t1 > MAX_TIME:
            continue

        if complete_road_conflict_check(pickup_steps, reservation_table, agv, scoring_system):
            continue

        # 2. 取料动作
        pickup_action = {
            "timestamp": t1,
            "name": agv,
            "X": pos1[0],
            "Y": pos1[1],
            "pitch": pitch1,
            "loaded": "true",
            "destination": ep,
            "Emergency": emergency,
            "task-id": taskid
        }
        t1 += 1

        # 3. 到卸货点的路径
        delivery_path = complete_road_a_star(pos1, delivery_point, road_network)
        if not delivery_path:
            continue

        delivery_steps, t2, pitch2, pos2 = complete_road_simulate_path(
            agv, delivery_path, t1, pitch1, True, ep, emergency
        )

        if not delivery_steps or t2 > MAX_TIME:
            continue

        if complete_road_conflict_check(delivery_steps, reservation_table, agv, scoring_system):
            continue

        # 4. 卸货动作
        delivery_action = {
            "timestamp": t2,
            "name": agv,
            "X": pos2[0],
            "Y": pos2[1],
            "pitch": pitch2,
            "loaded": "false",
            "destination": "",
            "Emergency": "false",
            "task-id": taskid
        }
        t2 += 1

        # 5. 成功，预约路径并写入结果
        all_steps = pickup_steps + [pickup_action] + delivery_steps + [delivery_action]

        # 预约所有步骤
        for step in all_steps:
            key = (step["timestamp"], (step["X"], step["Y"]))
            reservation_table[key] = agv

        append_to_csv(all_steps)

        # 更新AGV状态
        agv_states[agv] = {
            "pos": pos2,
            "pitch": pitch2,
            "time": t2,
            "home": state["home"]
        }

        # 记录任务信息
        task_info = {
            "task_id": taskid,
            "agv": agv,
            "start_point": sp,
            "end_point": ep,
            "priority": priority,
            "remaining_time": remaining_time,
            "completion_time": t2,
            "start_time": state["time"]
        }

        scoring_system.add_completed_task(task_info)

        if emergency and remaining_time is not None:
            scoring_system.add_urgent_task(task_info)

        print(f"[SUCCESS] AGV {agv} 完成任务 {taskid}: {sp} -> {ep} (时间: {state['time']}->{t2})")

        return task_info

    return None

def cleanup_old_reservations_complete_road(reservation_table, agv_states):
    """清理过期预约"""
    current_min_time = min(state["time"] for state in agv_states.values())
    cutoff_time = current_min_time - 50  # 保留更多历史预约

    keys_to_remove = [key for key in reservation_table.keys() if key[0] < cutoff_time]
    for key in keys_to_remove:
        del reservation_table[key]

    if keys_to_remove:
        print(f"[INFO] 清理了{len(keys_to_remove)}个过期预约")

def finalize_trajectory_csv_complete_road(agv_states, final_time, scoring_system):
    """完成轨迹CSV文件"""
    with open(CSV_PATH, 'a', newline='') as f:
        writer = csv.writer(f)
        for agv_id, state in agv_states.items():
            if scoring_system.agv_status.get(agv_id) == 'disappeared':
                continue

            last_time = state["time"]
            for t in range(last_time, final_time + 1):
                writer.writerow([t, agv_id, state["pos"][0], state["pos"][1], state["pitch"], "false", "", "false"])

    # 排序
    df = pd.read_csv(CSV_PATH)
    df.sort_values(by=["timestamp", "name"], inplace=True)
    df.to_csv(CSV_PATH, index=False)
    print(f"[INFO] 轨迹文件已保存到: {CSV_PATH}")

    # 碰撞检测
    detect_complete_road_collisions(CSV_PATH, COLLISION_PATH, scoring_system)

def detect_complete_road_collisions(csv_path, output_path, scoring_system):
    """完整道路网络的碰撞检测"""
    position_states = defaultdict(dict)
    agv_positions = defaultdict(dict)

    with open(csv_path, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            timestamp = int(row['timestamp'])
            x, y = int(row['X']), int(row['Y'])
            agv = row['name']
            position_states[timestamp][(x, y)] = agv
            agv_positions[agv][timestamp] = (x, y)

    collisions = []

    # 静态冲突检测
    for timestamp in position_states:
        pos_counts = defaultdict(list)
        for pos, agv in position_states[timestamp].items():
            pos_counts[pos].append(agv)

        for pos, agvs in pos_counts.items():
            if len(agvs) > 1:
                collision_info = {
                    "timestamp": timestamp,
                    "X": pos[0],
                    "Y": pos[1],
                    "type": "static",
                    "AGVs": ", ".join(agvs)
                }
                collisions.append(collision_info)
                scoring_system.add_collision(collision_info)

    # 对穿冲突检测（应该被完全避免）
    seen_crossings = set()
    for agv1 in agv_positions:
        for agv2 in agv_positions:
            if agv1 >= agv2:
                continue
            for t in agv_positions[agv1]:
                if (t + 1 not in agv_positions[agv1]) or (t not in agv_positions[agv2]) or (t + 1 not in agv_positions[agv2]):
                    continue

                p1_now = agv_positions[agv1][t]
                p1_next = agv_positions[agv1][t + 1]
                p2_now = agv_positions[agv2][t]
                p2_next = agv_positions[agv2][t + 1]

                if p1_now == p2_next and p2_now == p1_next:
                    key = (t, tuple(sorted([agv1, agv2])))
                    if key not in seen_crossings:
                        seen_crossings.add(key)
                        collision_info = {
                            "timestamp": t,
                            "X": p1_now[0],
                            "Y": p1_now[1],
                            "type": "crossing",
                            "AGVs": f"{agv1}, {agv2}"
                        }
                        collisions.append(collision_info)
                        scoring_system.add_collision(collision_info)

    # 保存结果
    with open(output_path, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=["timestamp", "X", "Y", "type", "AGVs"])
        writer.writeheader()
        writer.writerows(collisions)

    if collisions:
        crossing_collisions = [c for c in collisions if c['type'] == 'crossing']
        static_collisions = [c for c in collisions if c['type'] == 'static']
        print(f"[INFO] 检测到 {len(collisions)} 个碰撞事件")
        print(f"[INFO] 对穿冲突: {len(crossing_collisions)}个, 静态冲突: {len(static_collisions)}个")

        if len(crossing_collisions) == 0:
            print(f"[SUCCESS] 🎉 完全消除对穿冲突！")
    else:
        print(f"[SUCCESS] 🎉🎉🎉 完整道路网络实现零碰撞！🎉🎉🎉")

    print(f"[INFO] 碰撞检测结果已保存到: {output_path}")

if __name__ == '__main__':
    print("=" * 70)
    print("🛣️  AGV完整道路网络任务分配系统 V5")
    print("🎯 目标：彻底解决对穿冲突，实现零碰撞高效率")
    print("=" * 70)

    data_dir = os.path.join(os.path.dirname(__file__), '赛题附件-输入输出格式说明')
    task_file = os.path.join(data_dir, '附件2——task_csv.csv')
    map_file = os.path.join(data_dir, '附件1——map_data.csv')

    start_time = time.time()
    result, score_details = complete_road_assign_tasks(task_file=task_file, map_file=map_file)
    end_time = time.time()

    print("\n" + "=" * 70)
    print("📊 完整道路网络系统评分结果")
    print("=" * 70)
    print(f"✅ 基础分数: {score_details['base_score']}")
    print(f"🎯 高优先级奖励: +{score_details['urgent_bonus']}")
    print(f"⚠️  高优先级惩罚: -{score_details['urgent_penalty']}")
    print(f"💥 碰撞惩罚: -{score_details['collision_penalty']}")
    print(f"🛣️  道路违规次数: {score_details['road_violations']}")
    print(f"🛡️  成功避让次数: {score_details['successful_avoidance']}")
    print(f"🏆 总分: {score_details['total_score']}/120")
    print(f"⏱️  最后完成时间: {score_details['last_completion_time']}秒")
    print(f"⚡ 运行时间: {end_time - start_time:.2f}秒")

    if score_details['collision_count'] == 0:
        print("\n🎉🎉🎉 恭喜！完整道路网络实现零碰撞！🎉🎉🎉")
    else:
        print(f"\n📊 碰撞分析: 总计 {score_details['collision_count']} 次碰撞")
        print("🔧 建议进一步优化道路网络设计")

    print("=" * 70)
