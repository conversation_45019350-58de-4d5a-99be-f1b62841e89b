import matplotlib.pyplot as plt
import matplotlib.patches as patches
import pandas as pd
import numpy as np
from matplotlib.animation import FuncAnimation
import csv

class AGVVisualizer:
    def __init__(self, map_file, trajectory_file):
        self.map_file = map_file
        self.trajectory_file = trajectory_file
        self.start_points = {}
        self.end_points = {}
        self.agv_list = []
        self.trajectory_data = None
        self.colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 
                      'pink', 'gray', 'olive', 'cyan', 'magenta', 'yellow']
        
    def load_map_data(self):
        """加载地图数据"""
        with open(self.map_file, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                t, name = row["type"].strip(), row["name"].strip()
                x, y = int(row["x"]), int(row["y"])
                if t == "start_point":
                    self.start_points[name] = (x, y)
                elif t == "end_point":
                    self.end_points[name] = (x, y)
                elif t == "agv":
                    self.agv_list.append({
                        "name": name,
                        "x": x,
                        "y": y,
                        "pitch": int(row["pitch"]) if row["pitch"] else 0
                    })
    
    def load_trajectory_data(self):
        """加载轨迹数据"""
        self.trajectory_data = pd.read_csv(self.trajectory_file)
        self.trajectory_data = self.trajectory_data.sort_values(['timestamp', 'name'])
        
    def plot_static_map(self):
        """绘制静态地图"""
        fig, ax = plt.subplots(figsize=(14, 14))
        
        # 设置网格
        ax.set_xlim(0, 21)
        ax.set_ylim(0, 21)
        ax.set_aspect('equal')
        
        # 绘制详细的网格格子
        for i in range(22):
            ax.axhline(i, color='gray', linewidth=0.5, alpha=0.7)
            ax.axvline(i, color='gray', linewidth=0.5, alpha=0.7)
        
        # 绘制主要网格（每5格加粗）
        for i in range(0, 22, 5):
            ax.axhline(i, color='black', linewidth=1, alpha=0.3)
            ax.axvline(i, color='black', linewidth=1, alpha=0.3)
        
        # 设置网格刻度
        ax.set_xticks(range(0, 22))
        ax.set_yticks(range(0, 22))
        ax.set_xticklabels(range(0, 22))
        ax.set_yticklabels(range(0, 22))
        
        # 绘制起始点（覆盖整个格子）
        for name, (x, y) in self.start_points.items():
            rect = patches.Rectangle((x-0.5, y-0.5), 1.0, 1.0, 
                                   linewidth=2, edgecolor='darkgreen', 
                                   facecolor='lightgreen', alpha=0.8)
            ax.add_patch(rect)
            ax.text(x, y, name, ha='center', va='center', fontsize=8, weight='bold')
        
        # 绘制终点（覆盖整个格子）
        for name, (x, y) in self.end_points.items():
            rect = patches.Rectangle((x-0.5, y-0.5), 1.0, 1.0, 
                                   linewidth=2, edgecolor='darkred', 
                                   facecolor='lightcoral', alpha=0.8)
            ax.add_patch(rect)
            ax.text(x, y, name, ha='center', va='center', fontsize=7, weight='bold')
        
        # 绘制AGV初始位置
        for i, agv in enumerate(self.agv_list):
            color = self.colors[i % len(self.colors)]
            circle = patches.Circle((agv['x'], agv['y']), 0.35, 
                                  facecolor=color, edgecolor='black', 
                                  linewidth=2, alpha=0.9)
            ax.add_patch(circle)
            ax.text(agv['x'], agv['y']+0.7, agv['name'], 
                   ha='center', va='center', fontsize=9, weight='bold',
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
        
        ax.set_title('AGV调度系统地图 (网格视图)', fontsize=16, weight='bold')
        ax.set_xlabel('X坐标 (网格)', fontsize=12)
        ax.set_ylabel('Y坐标 (网格)', fontsize=12)
        
        # 添加图例
        legend_elements = [
            patches.Patch(color='lightgreen', label='起始点'),
            patches.Patch(color='lightcoral', label='终点'),
            patches.Patch(color='gray', label='AGV初始位置')
        ]
        ax.legend(handles=legend_elements, loc='upper right')
        
        plt.tight_layout()
        plt.show()
        
    def plot_trajectory_at_time(self, timestamp):
        """绘制指定时间点的轨迹"""
        fig, ax = plt.subplots(figsize=(14, 14))
        
        # 设置网格
        ax.set_xlim(0, 21)
        ax.set_ylim(0, 21)
        ax.set_aspect('equal')
        
        # 绘制详细的网格格子
        for i in range(22):
            ax.axhline(i, color='gray', linewidth=0.5, alpha=0.7)
            ax.axvline(i, color='gray', linewidth=0.5, alpha=0.7)
        
        # 绘制主要网格（每5格加粗）
        for i in range(0, 22, 5):
            ax.axhline(i, color='black', linewidth=1, alpha=0.3)
            ax.axvline(i, color='black', linewidth=1, alpha=0.3)
        
        # 设置网格刻度
        ax.set_xticks(range(0, 22))
        ax.set_yticks(range(0, 22))
        ax.set_xticklabels(range(0, 22))
        ax.set_yticklabels(range(0, 22))
        
        # 绘制起始点和终点
        for name, (x, y) in self.start_points.items():
            rect = patches.Rectangle((x-0.5, y-0.5), 1.0, 1.0, 
                                   linewidth=2, edgecolor='darkgreen', 
                                   facecolor='lightgreen', alpha=0.6)
            ax.add_patch(rect)
            ax.text(x, y, name, ha='center', va='center', fontsize=7, weight='bold')
        
        for name, (x, y) in self.end_points.items():
            rect = patches.Rectangle((x-0.5, y-0.5), 1.0, 1.0, 
                                   linewidth=2, edgecolor='darkred', 
                                   facecolor='lightcoral', alpha=0.6)
            ax.add_patch(rect)
            ax.text(x, y, name, ha='center', va='center', fontsize=6, weight='bold')
        
        # 获取指定时间点的AGV位置
        current_data = self.trajectory_data[self.trajectory_data['timestamp'] == timestamp]
        
        # 绘制AGV当前位置
        for i, (_, row) in enumerate(current_data.iterrows()):
            color = self.colors[i % len(self.colors)]
            
            # AGV圆圈
            circle = patches.Circle((row['X'], row['Y']), 0.35, 
                                  facecolor=color, edgecolor='black', 
                                  linewidth=2, alpha=0.9)
            ax.add_patch(circle)
            
            # AGV名称
            ax.text(row['X'], row['Y']+0.6, row['name'], 
                   ha='center', va='center', fontsize=9, weight='bold',
                   bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
            
            # 显示状态信息
            status = f"载货: {row['loaded']}"
            if row['destination']:
                status += f"\n目标: {row['destination']}"
            if row['Emergency'] == 'true':
                status += "\n⚠️紧急!"
                
            ax.text(row['X'], row['Y']-0.8, status, 
                   ha='center', va='center', fontsize=7, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7,
                           edgecolor='black', linewidth=1))
        
        ax.set_title(f'AGV位置 - 时间: {timestamp}秒 (网格视图)', fontsize=16, weight='bold')
        ax.set_xlabel('X坐标 (网格)', fontsize=12)
        ax.set_ylabel('Y坐标 (网格)', fontsize=12)
        
        plt.tight_layout()
        plt.show()
        
    def create_animation(self, max_time=None, interval=500):
        """创建动画"""
        if max_time is None:
            max_time = self.trajectory_data['timestamp'].max()
        
        fig, ax = plt.subplots(figsize=(14, 14))
        ax.set_xlim(0, 21)
        ax.set_ylim(0, 21)
        ax.set_aspect('equal')
        
        # 绘制详细的网格格子
        for i in range(22):
            ax.axhline(i, color='gray', linewidth=0.5, alpha=0.7)
            ax.axvline(i, color='gray', linewidth=0.5, alpha=0.7)
        
        # 绘制主要网格（每5格加粗）
        for i in range(0, 22, 5):
            ax.axhline(i, color='black', linewidth=1, alpha=0.3)
            ax.axvline(i, color='black', linewidth=1, alpha=0.3)
        
        # 设置网格刻度
        ax.set_xticks(range(0, 22))
        ax.set_yticks(range(0, 22))
        ax.set_xticklabels(range(0, 22))
        ax.set_yticklabels(range(0, 22))
        
        # 绘制固定的起始点和终点（正方形覆盖整个格子）
        for name, (x, y) in self.start_points.items():
            rect = patches.Rectangle((x-0.5, y-0.5), 1.0, 1.0, 
                                   linewidth=2, edgecolor='darkgreen', 
                                   facecolor='lightgreen', alpha=0.8)
            ax.add_patch(rect)
            ax.text(x, y, name, ha='center', va='center', fontsize=7, weight='bold')
        
        for name, (x, y) in self.end_points.items():
            rect = patches.Rectangle((x-0.5, y-0.5), 1.0, 1.0, 
                                   linewidth=2, edgecolor='darkred', 
                                   facecolor='lightcoral', alpha=0.8)
            ax.add_patch(rect)
            ax.text(x, y, name, ha='center', va='center', fontsize=6, weight='bold')
        
        # 初始化AGV圆圈和文本
        agv_circles = []
        agv_texts = []
        agv_status_texts = []
        
        def animate(frame):
            # 清除之前的AGV
            for circle in agv_circles:
                circle.remove()
            for text in agv_texts:
                text.remove()
            for text in agv_status_texts:
                text.remove()
            
            agv_circles.clear()
            agv_texts.clear()
            agv_status_texts.clear()
            
            # 获取当前时间点的数据
            current_data = self.trajectory_data[self.trajectory_data['timestamp'] == frame]
            
            # 绘制AGV（居中在格子中）
            for i, (_, row) in enumerate(current_data.iterrows()):
                color = self.colors[i % len(self.colors)]
                
                # AGV圆圈（稍微大一些，居中在格子中）
                circle = patches.Circle((row['X'], row['Y']), 0.35, 
                                      facecolor=color, edgecolor='black', 
                                      linewidth=2, alpha=0.9)
                ax.add_patch(circle)
                agv_circles.append(circle)
                
                # AGV名称（在圆圈上方）
                text = ax.text(row['X'], row['Y']+0.6, row['name'], 
                             ha='center', va='center', fontsize=9, weight='bold',
                             bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.8))
                agv_texts.append(text)
                
                # 状态信息（在圆圈下方）
                status = f"载货: {row['loaded']}"
                if row['destination']:
                    status += f"\n目标: {row['destination']}"
                if row['Emergency'] == 'true':
                    status += "\n⚠️紧急!"
                
                status_text = ax.text(row['X'], row['Y']-0.8, status, 
                                    ha='center', va='center', fontsize=7,
                                    bbox=dict(boxstyle="round,pad=0.3", 
                                            facecolor=color, alpha=0.7,
                                            edgecolor='black', linewidth=1))
                agv_status_texts.append(status_text)
            
            # 更新标题显示当前时间和AGV总数
            ax.set_title(f'AGV实时轨迹 - 时间: {frame}秒 | AGV总数: {len(current_data)}', 
                        fontsize=16, weight='bold')
            ax.set_xlabel('X坐标 (网格)', fontsize=12)
            ax.set_ylabel('Y坐标 (网格)', fontsize=12)
            
        # 创建动画（调慢速度，interval=500ms）
        anim = FuncAnimation(fig, animate, frames=range(0, min(max_time+1, 200)), 
                           interval=interval, repeat=True)
        
        plt.tight_layout()
        plt.show()
        
        return anim

# 使用示例
if __name__ == "__main__":
    # 文件路径
    map_file = "赛题附件-输入输出格式说明/附件1——map_data.csv"
    trajectory_file = "agv_trajectory.csv"
    
    # 创建可视化器
    visualizer = AGVVisualizer(map_file, trajectory_file)
    
    # 加载数据
    visualizer.load_map_data()
    visualizer.load_trajectory_data()
    
    # 绘制静态地图
    print("绘制静态地图...")
    visualizer.plot_static_map()
    
    # 绘制特定时间点的轨迹
    print("绘制时间点50的轨迹...")
    visualizer.plot_trajectory_at_time(50)
    
    # 创建动画（前100秒）
    print("创建动画...")
    anim = visualizer.create_animation(max_time=100, interval=100)
    
    # 保存动画（可选）
    # anim.save('agv_animation.gif', writer='pillow', fps=10)
