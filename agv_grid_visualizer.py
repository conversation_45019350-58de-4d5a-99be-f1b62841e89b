import matplotlib.pyplot as plt
import matplotlib.patches as patches
import pandas as pd
import numpy as np
from matplotlib.animation import FuncAnimation
import csv

class AGVVisualizerOptimized:
    def __init__(self, map_file, trajectory_file):
        self.map_file = map_file
        self.trajectory_file = trajectory_file
        self.start_points = {}
        self.end_points = {}
        self.agv_list = []
        self.trajectory_data = None
        self.colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 
                      'pink', 'gray', 'olive', 'cyan', 'magenta', 'yellow']
        
    def load_map_data(self):
        """Load map data"""
        with open(self.map_file, 'r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                t, name = row["type"].strip(), row["name"].strip()
                x, y = int(row["x"]), int(row["y"])
                if t == "start_point":
                    self.start_points[name] = (x, y)
                elif t == "end_point":
                    self.end_points[name] = (x, y)
                elif t == "agv":
                    self.agv_list.append({
                        "name": name,
                        "x": x,
                        "y": y,
                        "pitch": int(row["pitch"]) if row["pitch"] else 0
                    })
    
    def load_trajectory_data(self):
        """Load trajectory data"""
        self.trajectory_data = pd.read_csv(self.trajectory_file)
        self.trajectory_data = self.trajectory_data.sort_values(['timestamp', 'name'])
        
    def create_grid_animation(self, max_time=None, interval=800):
        """Create optimized grid animation"""
        if max_time is None:
            max_time = self.trajectory_data['timestamp'].max()
        
        fig, ax = plt.subplots(figsize=(15, 15))
        ax.set_xlim(0, 21)
        ax.set_ylim(0, 21)
        ax.set_aspect('equal')
        
        # Draw detailed grid
        for i in range(22):
            ax.axhline(i, color='lightgray', linewidth=0.5, alpha=0.7)
            ax.axvline(i, color='lightgray', linewidth=0.5, alpha=0.7)
        
        # Draw major grid lines (every 5 units)
        for i in range(0, 22, 5):
            ax.axhline(i, color='gray', linewidth=1, alpha=0.5)
            ax.axvline(i, color='gray', linewidth=1, alpha=0.5)
        
        # Set grid ticks
        ax.set_xticks(range(0, 22))
        ax.set_yticks(range(0, 22))
        ax.set_xticklabels(range(0, 22), fontsize=10)
        ax.set_yticklabels(range(0, 22), fontsize=10)
        
        # Draw fixed start points and end points
        for name, (x, y) in self.start_points.items():
            rect = patches.Rectangle((x-0.5, y-0.5), 1.0, 1.0, 
                                   linewidth=2, edgecolor='darkgreen', 
                                   facecolor='lightgreen', alpha=0.8)
            ax.add_patch(rect)
            ax.text(x, y, name, ha='center', va='center', fontsize=8, weight='bold')
        
        for name, (x, y) in self.end_points.items():
            rect = patches.Rectangle((x-0.5, y-0.5), 1.0, 1.0, 
                                   linewidth=2, edgecolor='darkred', 
                                   facecolor='lightcoral', alpha=0.8)
            ax.add_patch(rect)
            ax.text(x, y, name, ha='center', va='center', fontsize=7, weight='bold')
        
        # Initialize AGV elements
        agv_circles = []
        agv_texts = []
        agv_status_texts = []
        
        def animate(frame):
            # Clear previous AGVs
            for circle in agv_circles:
                circle.remove()
            for text in agv_texts:
                text.remove()
            for text in agv_status_texts:
                text.remove()
            
            agv_circles.clear()
            agv_texts.clear()
            agv_status_texts.clear()
            
            # Get current frame data
            current_data = self.trajectory_data[self.trajectory_data['timestamp'] == frame]
            
            # Draw AGVs
            for i, (_, row) in enumerate(current_data.iterrows()):
                color = self.colors[i % len(self.colors)]
                
                # AGV circle (centered in grid cell)
                circle = patches.Circle((row['X'], row['Y']), 0.35, 
                                      facecolor=color, edgecolor='black', 
                                      linewidth=2, alpha=0.9)
                ax.add_patch(circle)
                agv_circles.append(circle)
                
                # AGV name (above circle)
                text = ax.text(row['X'], row['Y']+0.6, row['name'], 
                             ha='center', va='center', fontsize=9, weight='bold',
                             bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.9))
                agv_texts.append(text)
                
                # Status info (below circle)
                status = f"Load: {row['loaded']}"
                if row['destination'] and row['destination'] != '':
                    status += f"\\nTo: {row['destination']}"
                if row['Emergency'] == 'true':
                    status += "\\n⚠️ URGENT!"
                
                status_text = ax.text(row['X'], row['Y']-0.8, status, 
                                    ha='center', va='center', fontsize=8,
                                    bbox=dict(boxstyle="round,pad=0.3", 
                                            facecolor=color, alpha=0.7,
                                            edgecolor='black', linewidth=1))
                agv_status_texts.append(status_text)
            
            # Update title
            ax.set_title(f'AGV Real-time Tracking - Time: {frame}s | Total AGVs: {len(current_data)}', 
                        fontsize=16, weight='bold')
            ax.set_xlabel('X Coordinate (Grid)', fontsize=12)
            ax.set_ylabel('Y Coordinate (Grid)', fontsize=12)
            
        # Create animation (slower speed)
        anim = FuncAnimation(fig, animate, frames=range(0, min(max_time+1, 150)), 
                           interval=interval, repeat=True)
        
        plt.tight_layout()
        plt.show()
        
        return anim
    
    def quick_demo(self):
        """Quick demonstration of key time points"""
        fig, axes = plt.subplots(2, 2, figsize=(20, 20))
        axes = axes.flatten()
        
        times = [0, 50, 100, 150]
        
        for idx, t in enumerate(times):
            ax = axes[idx]
            
            # Setup grid
            ax.set_xlim(0, 21)
            ax.set_ylim(0, 21)
            ax.set_aspect('equal')
            
            # Draw grid
            for i in range(22):
                ax.axhline(i, color='lightgray', linewidth=0.5, alpha=0.7)
                ax.axvline(i, color='lightgray', linewidth=0.5, alpha=0.7)
            
            # Draw major grid
            for i in range(0, 22, 5):
                ax.axhline(i, color='gray', linewidth=1, alpha=0.5)
                ax.axvline(i, color='gray', linewidth=1, alpha=0.5)
            
            ax.set_xticks(range(0, 22))
            ax.set_yticks(range(0, 22))
            
            # Draw start and end points
            for name, (x, y) in self.start_points.items():
                rect = patches.Rectangle((x-0.5, y-0.5), 1.0, 1.0, 
                                       linewidth=2, edgecolor='darkgreen', 
                                       facecolor='lightgreen', alpha=0.8)
                ax.add_patch(rect)
                ax.text(x, y, name, ha='center', va='center', fontsize=6, weight='bold')
            
            for name, (x, y) in self.end_points.items():
                rect = patches.Rectangle((x-0.5, y-0.5), 1.0, 1.0, 
                                       linewidth=2, edgecolor='darkred', 
                                       facecolor='lightcoral', alpha=0.8)
                ax.add_patch(rect)
                ax.text(x, y, name, ha='center', va='center', fontsize=5, weight='bold')
            
            # Draw AGVs at time t
            current_data = self.trajectory_data[self.trajectory_data['timestamp'] == t]
            for i, (_, row) in enumerate(current_data.iterrows()):
                color = self.colors[i % len(self.colors)]
                
                circle = patches.Circle((row['X'], row['Y']), 0.35, 
                                      facecolor=color, edgecolor='black', 
                                      linewidth=2, alpha=0.9)
                ax.add_patch(circle)
                
                ax.text(row['X'], row['Y']+0.6, row['name'], 
                       ha='center', va='center', fontsize=7, weight='bold',
                       bbox=dict(boxstyle="round,pad=0.2", facecolor='white', alpha=0.9))
            
            ax.set_title(f'Time: {t}s', fontsize=14, weight='bold')
            ax.set_xlabel('X (Grid)', fontsize=10)
            ax.set_ylabel('Y (Grid)', fontsize=10)
        
        plt.tight_layout()
        plt.show()

# Usage example
if __name__ == "__main__":
    # File paths
    map_file = "赛题附件-输入输出格式说明/附件1——map_data.csv"
    trajectory_file = "agv_trajectory.csv"
    
    # Create visualizer
    visualizer = AGVVisualizerOptimized(map_file, trajectory_file)
    
    # Load data
    visualizer.load_map_data()
    visualizer.load_trajectory_data()
    
    print("Creating grid-based visualization...")
    print("1. Quick demo of key time points...")
    visualizer.quick_demo()
    
    print("2. Creating animated visualization...")
    print("   - Grid layout with clear cell boundaries")
    print("   - AGVs positioned in grid centers")
    print("   - Slower animation speed for better viewing")
    print("   - Real-time status updates")
    
    # Create animation
    anim = visualizer.create_grid_animation(max_time=100, interval=800)
    
    # Optionally save animation
    # anim.save('agv_grid_animation.gif', writer='pillow', fps=2)
