# Test Runner V2 - 初赛评分系统

## 概述

`test_runner_v2.py` 是基于原始 `test_runner.py` 的增强版本，专门为初赛评分规则设计。该版本在原有功能基础上增加了完整的评分系统，能够按照初赛规则自动计算得分。

## 主要功能

### 1. 评分系统 (ScoringSystem 类)

实现了完整的初赛评分规则：

- **基础分数**：正确运达的快件数量，每个快件1分
- **高优先级任务奖励**：在指定时间内完成的高优先级任务，每个+10分
- **高优先级任务惩罚**：未在指定时间内完成的高优先级任务，每个-5分
- **AGV碰撞惩罚**：每次碰撞-10分，碰撞的AGV消失且无法继续使用
- **时间限制**：5分钟(300秒)内的任务才计分
- **平分比较**：记录最后一个快件任务完成时间

### 2. 增强的碰撞检测

- 检测静态碰撞（多个AGV同时占用同一位置）
- 检测对穿冲突（两个AGV交换位置）
- 自动将碰撞信息集成到评分系统

### 3. 输出文件

程序运行后会生成以下文件：

- `agv_trajectory_v2.csv`：AGV轨迹文件
- `agv_collisions_v2.csv`：碰撞检测结果
- `scoring_results_v2.csv`：详细评分结果

## 评分规则详解

### 满分构成
- 基础分数：100分（正确运达快件数量）
- 额外附加分：20分（高优先级任务奖励）
- **总满分：120分**

### 计分规则
1. **基础分数**：在5分钟内统计被运达正确流向的所有快件，一个快件计1分
2. **高优先级任务**：
   - 在指定剩余时间内完成：+10分/任务
   - 未在指定时间内完成：-5分/任务
3. **AGV碰撞**：
   - 发生碰撞：-10分/次
   - AGV消失，无法继续使用
   - 相关快件任务丢失，不可继续执行
4. **违反约束**：违反约束的任务不得分

### 平分处理
- 如果总分相同，比较最后一个快件任务完成的时间
- 时间靠前者胜出

## 使用方法

```bash
python test_runner_v2.py
```

程序会自动：
1. 读取任务和地图数据
2. 执行AGV路径规划和任务分配
3. 检测碰撞
4. 计算评分
5. 生成结果文件

## 输出示例

```
=== 初赛评分结果 ===
基础分数（正确运达快件）: 54
高优先级任务奖励: +0
高优先级任务惩罚: -10
碰撞惩罚: -60
总分: -16/120
最后任务完成时间: 344秒
==================
```

## 主要改进

1. **时间限制控制**：添加了300秒时间限制和最大迭代次数限制，避免无限循环
2. **碰撞处理**：碰撞的AGV会被标记为消失状态，无法继续执行任务
3. **评分集成**：所有任务完成和碰撞事件都会自动记录到评分系统
4. **详细报告**：生成详细的评分报告和碰撞报告

## 注意事项

- 程序使用固定随机种子(42)确保结果可重现
- 当所有AGV都超过时间限制时会自动停止任务分配
- 评分系统严格按照初赛规则实现，确保评分准确性
- 生成的CSV文件格式符合比赛要求

## 文件结构

- `test_runner_v2.py`：主程序文件
- `agv_trajectory_v2.csv`：AGV轨迹输出
- `agv_collisions_v2.csv`：碰撞检测结果
- `scoring_results_v2.csv`：评分结果详情
- `README_test_runner_v2.md`：本说明文档
