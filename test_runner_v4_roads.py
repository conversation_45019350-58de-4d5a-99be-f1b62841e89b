import json
import csv
import random
import pandas as pd
import heapq
from collections import defaultdict
import os
import time

# V4版本：基于道路规则的零碰撞系统
TURN_COST = 1
LOAD_UNLOAD_TIME = 1
GRID_SIZE = (21, 21)
CSV_PATH = "agv_trajectory_v4_roads.csv"
COLLISION_PATH = "agv_collisions_v4_roads.csv"
SCORING_PATH = "scoring_results_v4_roads.csv"

# 评分系统常量
MAX_TIME = 300
BASE_SCORE = 100
BONUS_SCORE = 20
URGENT_BONUS = 10
URGENT_PENALTY = 5
COLLISION_PENALTY = 10

random.seed(42)

class RoadBasedScoringSystem:
    """基于道路规则的评分系统"""
    
    def __init__(self):
        self.completed_tasks = []
        self.urgent_tasks = []
        self.collisions = []
        self.agv_status = {}
        self.last_task_completion_time = 0
        self.road_violations = 0  # 道路违规次数
        
    def add_road_violation(self):
        """记录道路违规"""
        self.road_violations += 1
        
    def add_completed_task(self, task_info):
        self.completed_tasks.append(task_info)
        if task_info.get('completion_time', 0) > self.last_task_completion_time:
            self.last_task_completion_time = task_info.get('completion_time', 0)
            
    def add_urgent_task(self, task_info):
        self.urgent_tasks.append(task_info)
        
    def add_collision(self, collision_info):
        self.collisions.append(collision_info)
        if 'AGVs' in collision_info:
            agvs = collision_info['AGVs'].split(', ')
            for agv in agvs:
                self.agv_status[agv] = 'disappeared'
                
    def calculate_score(self):
        score_details = {
            'base_score': len([t for t in self.completed_tasks if t.get('completion_time', 0) <= MAX_TIME]),
            'urgent_bonus': 0,
            'urgent_penalty': 0,
            'collision_penalty': len(self.collisions) * COLLISION_PENALTY,
            'collision_count': len(self.collisions),
            'road_violations': self.road_violations,
            'last_completion_time': self.last_task_completion_time
        }
        
        # 处理紧急任务
        for urgent_task in self.urgent_tasks:
            remaining_time = urgent_task.get('remaining_time')
            completion_time = urgent_task.get('completion_time')
            
            if remaining_time is not None and completion_time is not None:
                if completion_time <= remaining_time:
                    score_details['urgent_bonus'] += URGENT_BONUS
                else:
                    score_details['urgent_penalty'] += URGENT_PENALTY
        
        score_details['total_score'] = (
            score_details['base_score'] + 
            score_details['urgent_bonus'] - 
            score_details['urgent_penalty'] - 
            score_details['collision_penalty']
        )
        
        return score_details
        
    def save_scoring_results(self, filepath=SCORING_PATH):
        score_details = self.calculate_score()
        
        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['评分项目', '数值', '说明'])
            writer.writerow(['基础分数', score_details['base_score'], f"正确运达快件数量"])
            writer.writerow(['高优先级奖励', score_details['urgent_bonus'], f"按时完成高优先级任务"])
            writer.writerow(['高优先级惩罚', -score_details['urgent_penalty'], f"未按时完成高优先级任务"])
            writer.writerow(['碰撞惩罚', -score_details['collision_penalty'], f"碰撞次数: {score_details['collision_count']}"])
            writer.writerow(['总分', score_details['total_score'], f"满分120分"])
            writer.writerow(['最后完成时间', score_details['last_completion_time'], '用于平分时比较'])
            writer.writerow(['道路违规次数', score_details['road_violations'], '违反道路规则的次数'])
            
        print(f"[INFO] 评分结果已保存到: {filepath}")
        print(f"[INFO] 总分: {score_details['total_score']}/120")
        print(f"[INFO] 碰撞次数: {score_details['collision_count']}")
        print(f"[INFO] 道路违规次数: {score_details['road_violations']}")
        
        return score_details

class RoadSystem:
    """道路系统类，定义道路规则和方向"""
    
    def __init__(self):
        # 根据附图定义道路规则
        self.road_rules = self._initialize_road_rules()
        self.valid_positions = self._get_valid_positions()
        
    def _initialize_road_rules(self):
        """初始化道路规则 - 基于实际地图坐标"""
        rules = {}

        # 根据实际地图数据调整道路规则
        # 外环道路系统

        # 左侧道路 (x=2, 向上)
        for y in range(2, 19):
            rules[(2, y)] = [(0, 1)]  # 只能向上

        # 上方道路 (y=18, 向右)
        for x in range(2, 19):
            rules[(x, 18)] = [(1, 0)]  # 只能向右

        # 右侧道路 (x=19, 向下)
        for y in range(2, 19):
            rules[(19, y)] = [(0, -1)]  # 只能向下

        # 下方道路 (y=2, 向左)
        for x in range(2, 19):
            rules[(x, 2)] = [(-1, 0)]  # 只能向左

        # 内部道路网络 - 连接各个终点
        # 水平主干道
        for y in [4, 8, 12, 16]:
            for x in range(3, 18):
                if (x, y) not in rules:  # 避免覆盖外环
                    rules[(x, y)] = [(-1, 0), (1, 0)]  # 双向

        # 垂直主干道
        for x in [6, 9, 12, 15]:
            for y in range(3, 18):
                if (x, y) not in rules:  # 避免覆盖外环
                    rules[(x, y)] = [(0, -1), (0, 1)]  # 双向

        # 连接道路
        connection_roads = [
            # 连接起始点到主干道
            (2, 6), (2, 10), (2, 14),  # 左侧起始点连接
            (19, 6), (19, 10), (19, 14),  # 右侧起始点连接

            # AGV初始位置连接
            (3, 1), (6, 1), (9, 1), (12, 1), (15, 1), (18, 1),  # 下方AGV位置
            (3, 20), (6, 20), (9, 20), (12, 20), (15, 20), (18, 20),  # 上方AGV位置

            # 垂直连接道路
            (3, 2), (3, 3), (3, 4), (3, 5), (3, 6), (3, 7), (3, 8), (3, 9), (3, 10),
            (3, 11), (3, 12), (3, 13), (3, 14), (3, 15), (3, 16), (3, 17), (3, 18), (3, 19),

            # 更多连接道路
            (4, 4), (4, 8), (4, 12), (4, 16),
            (5, 4), (5, 8), (5, 12), (5, 16),
            (7, 4), (7, 8), (7, 12), (7, 16),
            (8, 4), (8, 8), (8, 12), (8, 16),
            (10, 4), (10, 8), (10, 12), (10, 16),
            (11, 4), (11, 8), (11, 12), (11, 16),
            (13, 4), (13, 8), (13, 12), (13, 16),
            (14, 4), (14, 8), (14, 12), (14, 16),
            (16, 4), (16, 8), (16, 12), (16, 16),
            (17, 4), (17, 8), (17, 12), (17, 16),
        ]

        for x, y in connection_roads:
            if (x, y) not in rules:
                rules[(x, y)] = [(-1, 0), (1, 0), (0, -1), (0, 1)]  # 全方向

        return rules
    
    def _get_valid_positions(self):
        """获取所有有效位置"""
        valid = set(self.road_rules.keys())

        # 添加实际的终点位置（根据map_data.csv）
        terminals = [
            (6, 4), (6, 8), (6, 12), (6, 16),    # Beijing, Shanghai, Suzhou, Hangzhou
            (9, 4), (9, 8), (9, 12), (9, 16),    # Nanjing, Wuhan, Changsha, Guangzhou
            (12, 4), (12, 8), (12, 12), (12, 16), # Chengdu, Xiamen, Kunming, Urumqi
            (15, 4), (15, 8), (15, 12), (15, 16), # Shenzhen, Dalian, Tianjin, Chongqing
        ]
        valid.update(terminals)

        # 添加实际的起始点位置
        start_points = [
            (1, 6), (1, 10), (1, 14),    # Tiger, Dragon, Horse
            (20, 6), (20, 10), (20, 14)  # Rabbit, Ox, Monkey
        ]
        valid.update(start_points)

        # 添加AGV初始位置
        agv_positions = [
            (3, 1), (6, 1), (9, 1), (12, 1), (15, 1), (18, 1),  # 下方AGV
            (3, 20), (6, 20), (9, 20), (12, 20), (15, 20), (18, 20)  # 上方AGV
        ]
        valid.update(agv_positions)

        return valid
    
    def get_valid_moves(self, pos):
        """获取从当前位置可以移动的方向"""
        if pos in self.road_rules:
            return self.road_rules[pos]
        return []  # 无效位置
    
    def is_valid_move(self, from_pos, to_pos):
        """检查移动是否符合道路规则"""
        if from_pos not in self.valid_positions or to_pos not in self.valid_positions:
            return False
            
        dx = to_pos[0] - from_pos[0]
        dy = to_pos[1] - from_pos[1]
        
        # 只允许相邻位置移动
        if abs(dx) + abs(dy) != 1:
            return False
            
        valid_directions = self.get_valid_moves(from_pos)
        return (dx, dy) in valid_directions

def manhattan(p1, p2):
    return abs(p1[0] - p2[0]) + abs(p1[1] - p2[1])

def get_orientation(from_pos, to_pos):
    dx, dy = to_pos[0] - from_pos[0], to_pos[1] - from_pos[1]
    if dx > 0:
        return 0
    elif dx < 0:
        return 180
    elif dy > 0:
        return 90
    elif dy < 0:
        return 270
    else:
        return None

def road_based_a_star(start, goal, road_system):
    """基于道路规则的A*算法"""
    def get_neighbors(pos):
        neighbors = []
        valid_moves = road_system.get_valid_moves(pos)
        
        for dx, dy in valid_moves:
            new_pos = (pos[0] + dx, pos[1] + dy)
            if new_pos in road_system.valid_positions:
                neighbors.append(new_pos)
        
        return neighbors

    frontier = []
    heapq.heappush(frontier, (manhattan(start, goal), 0, start, [start]))
    visited = set()

    while frontier:
        _, cost, current, path = heapq.heappop(frontier)
        if current == goal:
            return path
        if current in visited:
            continue
        visited.add(current)
        
        for neighbor in get_neighbors(current):
            if neighbor not in visited:
                new_cost = cost + 1
                priority = new_cost + manhattan(neighbor, goal)
                heapq.heappush(frontier, (priority, new_cost, neighbor, path + [neighbor]))
    
    return []  # 无法找到路径

def road_simulate_path(name, path, start_time, initial_pitch, loaded, destination, emergency):
    """基于道路的路径模拟"""
    if not path or len(path) < 2:
        return [], start_time, initial_pitch, path[0] if path else None
        
    steps = []
    t = start_time
    pitch = initial_pitch
    last = path[0]

    for current in path[1:]:
        new_pitch = get_orientation(last, current)
        if new_pitch != pitch and new_pitch is not None:
            steps.append({
                "timestamp": t,
                "name": name,
                "X": last[0],
                "Y": last[1],
                "pitch": new_pitch,
                "loaded": loaded,
                "destination": destination if loaded else "",
                "Emergency": emergency
            })
            pitch = new_pitch
            t += 1

        steps.append({
            "timestamp": t,
            "name": name,
            "X": current[0],
            "Y": current[1],
            "pitch": pitch,
            "loaded": loaded,
            "destination": destination if loaded else "",
            "Emergency": emergency
        })
        last = current
        t += 1

    return steps, t, pitch, last

def road_conflict_check(path_steps, reservation_table, agv_name):
    """基于道路的冲突检测（主要检查静态冲突）"""
    for step in path_steps:
        timestamp = step["timestamp"]
        pos = (step["X"], step["Y"])
        
        # 检查位置冲突
        if (timestamp, pos) in reservation_table:
            if reservation_table[(timestamp, pos)] != agv_name:
                return True
    
    return False

def get_pickup_coord(start_point_name, original_coord):
    """获取取料坐标"""
    if start_point_name in ["Tiger", "Dragon", "Horse"]:
        return (original_coord[0] + 1, original_coord[1])
    else:
        return (original_coord[0] - 1, original_coord[1])

def get_delivery_options(dest_coord):
    """获取卸货选项"""
    x, y = dest_coord
    candidates = [(x+1, y), (x-1, y), (x, y+1), (x, y-1)]
    return [(x, y) for x, y in candidates if 1 <= x <= 20 and 1 <= y <= 20]

def init_csv(agv_list):
    with open(CSV_PATH, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(["timestamp", "name", "X", "Y", "pitch", "loaded", "destination", "Emergency"])
        for agv in agv_list:
            writer.writerow([0, agv["id"], agv["pose"][0], agv["pose"][1], agv["pitch"], "false", "", "false"])

def append_to_csv(steps):
    with open(CSV_PATH, 'a', newline='') as f:
        writer = csv.writer(f)
        for step in steps:
            writer.writerow([
                step["timestamp"], step["name"], step["X"], step["Y"], step["pitch"],
                str(step["loaded"]).lower(), step["destination"], str(step["Emergency"]).lower()
            ])

def road_based_assign_tasks(task_file, map_file):
    """基于道路规则的任务分配"""
    scoring_system = RoadBasedScoringSystem()
    road_system = RoadSystem()

    # 读取任务
    all_tasks = []
    with open(task_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            all_tasks.append({
                "task_id": row["task_id"],
                "start_point": row["start_point"].strip(),
                "end_point": row["end_point"].strip(),
                "priority": row["priority"],
                "remaining_time": int(row["remaining_time"]) if row["remaining_time"] not in [None, "", "None"] else None
            })

    # 读取地图
    start_points, end_points, agv_list = {}, {}, []
    with open(map_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            t, name = row["type"].strip(), row["name"].strip()
            x, y = int(row["x"]), int(row["y"])
            if t == "start_point":
                start_points[name] = (x, y)
            elif t == "end_point":
                end_points[name] = (x, y)
            elif t == "agv":
                agv_list.append({"id": name, "pose": (x, y), "pitch": int(row["pitch"])})

    init_csv(agv_list)

    # 构建任务队列
    task_queues = {}
    for task in all_tasks:
        sp = task["start_point"]
        if sp not in task_queues:
            task_queues[sp] = []
        task_queues[sp].append(task)

    # 初始化AGV状态
    agv_states = {
        agv["id"]: {
            "pos": tuple(agv["pose"]),
            "pitch": agv["pitch"],
            "time": 1,
            "home": tuple(agv["pose"])
        } for agv in agv_list
    }

    reservation_table = {}
    assigned_tasks = []

    # 预约AGV初始位置
    for agv in agv_list:
        reservation_table[(0, tuple(agv["pose"]))] = agv["id"]

    iteration_count = 0
    max_iterations = 800

    print("[INFO] 开始基于道路规则的任务分配...")
    print(f"[INFO] 道路系统包含 {len(road_system.valid_positions)} 个有效位置")

    while any(task_queues[sp] for sp in task_queues) and iteration_count < max_iterations:
        iteration_count += 1
        progress = False

        # 检查时间限制
        active_agvs = [agv for agv in agv_states.keys() if agv_states[agv]["time"] <= MAX_TIME]
        if not active_agvs:
            print(f"[INFO] 所有AGV都已超过时间限制({MAX_TIME}秒)")
            break

        # 按时间排序AGV
        active_agvs.sort(key=lambda agv: agv_states[agv]["time"])

        for agv in active_agvs:
            state = agv_states[agv]

            # 收集可用任务
            available_tasks = []
            for sp in task_queues.keys():
                if task_queues[sp]:
                    task = task_queues[sp][0]
                    start_coord = get_pickup_coord(sp, start_points[sp])

                    # 检查起始坐标是否在道路系统中
                    if start_coord not in road_system.valid_positions:
                        continue

                    # 计算路径长度作为距离估算
                    test_path = road_based_a_star(state["pos"], start_coord, road_system)
                    if not test_path:
                        continue

                    distance = len(test_path)

                    # 优先级计算
                    priority_score = 0 if task["priority"].lower() == "urgent" else distance
                    available_tasks.append((priority_score, sp, task))

            # 按优先级排序
            available_tasks.sort(key=lambda x: x[0])

            # 尝试分配任务
            for _, sp, task in available_tasks:
                if not task_queues[sp]:
                    continue

                success = try_assign_task_road_based(agv, task, sp, start_points, end_points,
                                                   agv_states, reservation_table, scoring_system, road_system)

                if success:
                    assigned_tasks.append(success)
                    task_queues[sp].pop(0)
                    progress = True
                    break

        # 进度报告
        if iteration_count % 100 == 0:
            remaining_tasks = sum(len(tasks) for tasks in task_queues.values())
            print(f"[INFO] 迭代{iteration_count}: 剩余{remaining_tasks}个任务, 已分配{len(assigned_tasks)}个任务")

        if not progress:
            # 清理过期预约
            if iteration_count % 50 == 0:
                cleanup_old_reservations_road(reservation_table, agv_states)

    # 完成处理
    final_time = max(state["time"] for state in agv_states.values())
    finalize_trajectory_csv_road(agv_states, final_time, scoring_system)

    score_details = scoring_system.save_scoring_results()

    print(f"[INFO] 基于道路规则的分配完成！")
    print(f"[INFO] 共分配任务数：{len(assigned_tasks)}")
    print(f"[INFO] 碰撞次数：{score_details['collision_count']}")
    print(f"[INFO] 道路违规次数：{score_details['road_violations']}")
    print(f"[INFO] 总分：{score_details['total_score']}/120")

    return assigned_tasks, score_details

def try_assign_task_road_based(agv, task, sp, start_points, end_points, agv_states, reservation_table, scoring_system, road_system):
    """基于道路规则的任务分配尝试"""
    state = agv_states[agv]
    ep = task["end_point"]
    priority = task["priority"]
    emergency = True if priority.lower() == "urgent" else False
    taskid = task["task_id"]
    remaining_time = task["remaining_time"]

    start_coord = get_pickup_coord(sp, start_points[sp])
    end_coord_main = end_points[ep]
    delivery_candidates = get_delivery_options(end_coord_main)

    # 过滤有效的卸货点
    valid_delivery_candidates = [d for d in delivery_candidates if d in road_system.valid_positions]
    if not valid_delivery_candidates:
        return None

    # 尝试每个卸货点
    for delivery_point in valid_delivery_candidates:
        # 1. 到取料点的路径
        pickup_path = road_based_a_star(state["pos"], start_coord, road_system)
        if not pickup_path:
            continue

        pickup_steps, t1, pitch1, pos1 = road_simulate_path(
            agv, pickup_path, state["time"], state["pitch"], False, "", False
        )

        if not pickup_steps or t1 > MAX_TIME:
            continue

        if road_conflict_check(pickup_steps, reservation_table, agv):
            continue

        # 2. 取料动作
        pickup_action = {
            "timestamp": t1,
            "name": agv,
            "X": pos1[0],
            "Y": pos1[1],
            "pitch": pitch1,
            "loaded": "true",
            "destination": ep,
            "Emergency": emergency,
            "task-id": taskid
        }
        t1 += 1

        # 3. 到卸货点的路径
        delivery_path = road_based_a_star(pos1, delivery_point, road_system)
        if not delivery_path:
            continue

        delivery_steps, t2, pitch2, pos2 = road_simulate_path(
            agv, delivery_path, t1, pitch1, True, ep, emergency
        )

        if not delivery_steps or t2 > MAX_TIME:
            continue

        if road_conflict_check(delivery_steps, reservation_table, agv):
            continue

        # 4. 卸货动作
        delivery_action = {
            "timestamp": t2,
            "name": agv,
            "X": pos2[0],
            "Y": pos2[1],
            "pitch": pitch2,
            "loaded": "false",
            "destination": "",
            "Emergency": "false",
            "task-id": taskid
        }
        t2 += 1

        # 5. 成功，预约路径并写入结果
        all_steps = pickup_steps + [pickup_action] + delivery_steps + [delivery_action]

        # 预约所有步骤
        for step in all_steps:
            key = (step["timestamp"], (step["X"], step["Y"]))
            reservation_table[key] = agv

        append_to_csv(all_steps)

        # 更新AGV状态
        agv_states[agv] = {
            "pos": pos2,
            "pitch": pitch2,
            "time": t2,
            "home": state["home"]
        }

        # 记录任务信息
        task_info = {
            "task_id": taskid,
            "agv": agv,
            "start_point": sp,
            "end_point": ep,
            "priority": priority,
            "remaining_time": remaining_time,
            "completion_time": t2,
            "start_time": state["time"]
        }

        scoring_system.add_completed_task(task_info)

        if emergency and remaining_time is not None:
            scoring_system.add_urgent_task(task_info)

        print(f"[SUCCESS] AGV {agv} 完成任务 {taskid}: {sp} -> {ep} (时间: {state['time']}->{t2})")

        return task_info

    return None

def cleanup_old_reservations_road(reservation_table, agv_states):
    """清理过期预约"""
    current_min_time = min(state["time"] for state in agv_states.values())
    cutoff_time = current_min_time - 30

    keys_to_remove = [key for key in reservation_table.keys() if key[0] < cutoff_time]
    for key in keys_to_remove:
        del reservation_table[key]

    if keys_to_remove:
        print(f"[INFO] 清理了{len(keys_to_remove)}个过期预约")

def finalize_trajectory_csv_road(agv_states, final_time, scoring_system):
    """完成轨迹CSV文件"""
    with open(CSV_PATH, 'a', newline='') as f:
        writer = csv.writer(f)
        for agv_id, state in agv_states.items():
            if scoring_system.agv_status.get(agv_id) == 'disappeared':
                continue

            last_time = state["time"]
            for t in range(last_time, final_time + 1):
                writer.writerow([t, agv_id, state["pos"][0], state["pos"][1], state["pitch"], "false", "", "false"])

    # 排序
    df = pd.read_csv(CSV_PATH)
    df.sort_values(by=["timestamp", "name"], inplace=True)
    df.to_csv(CSV_PATH, index=False)
    print(f"[INFO] 轨迹文件已保存到: {CSV_PATH}")

    # 碰撞检测
    detect_road_collisions(CSV_PATH, COLLISION_PATH, scoring_system)

def detect_road_collisions(csv_path, output_path, scoring_system):
    """基于道路的碰撞检测"""
    position_states = defaultdict(dict)

    with open(csv_path, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            timestamp = int(row['timestamp'])
            x, y = int(row['X']), int(row['Y'])
            agv = row['name']
            position_states[timestamp][(x, y)] = agv

    collisions = []

    # 静态冲突检测（道路系统中主要是这种冲突）
    for timestamp in position_states:
        pos_counts = defaultdict(list)
        for pos, agv in position_states[timestamp].items():
            pos_counts[pos].append(agv)

        for pos, agvs in pos_counts.items():
            if len(agvs) > 1:
                collision_info = {
                    "timestamp": timestamp,
                    "X": pos[0],
                    "Y": pos[1],
                    "type": "static",
                    "AGVs": ", ".join(agvs)
                }
                collisions.append(collision_info)
                scoring_system.add_collision(collision_info)

    # 保存结果
    with open(output_path, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=["timestamp", "X", "Y", "type", "AGVs"])
        writer.writeheader()
        writer.writerows(collisions)

    if collisions:
        print(f"[ERROR] 检测到 {len(collisions)} 个碰撞事件！")
    else:
        print(f"[SUCCESS] 🎉 基于道路规则实现零碰撞！")

    print(f"[INFO] 碰撞检测结果已保存到: {output_path}")

if __name__ == '__main__':
    print("=" * 60)
    print("🛣️  AGV道路规则任务分配系统 V4")
    print("=" * 60)

    data_dir = os.path.join(os.path.dirname(__file__), '赛题附件-输入输出格式说明')
    task_file = os.path.join(data_dir, '附件2——task_csv.csv')
    map_file = os.path.join(data_dir, '附件1——map_data.csv')

    start_time = time.time()
    result, score_details = road_based_assign_tasks(task_file=task_file, map_file=map_file)
    end_time = time.time()

    print("\n" + "=" * 60)
    print("📊 道路规则系统评分结果")
    print("=" * 60)
    print(f"✅ 基础分数: {score_details['base_score']}")
    print(f"🎯 高优先级奖励: +{score_details['urgent_bonus']}")
    print(f"⚠️  高优先级惩罚: -{score_details['urgent_penalty']}")
    print(f"💥 碰撞惩罚: -{score_details['collision_penalty']}")
    print(f"🛣️  道路违规次数: {score_details['road_violations']}")
    print(f"🏆 总分: {score_details['total_score']}/120")
    print(f"⏱️  最后完成时间: {score_details['last_completion_time']}秒")
    print(f"⚡ 运行时间: {end_time - start_time:.2f}秒")

    if score_details['collision_count'] == 0:
        print("\n🎉🎉🎉 恭喜！道路规则实现零碰撞！🎉🎉🎉")
    else:
        print(f"\n❌ 仍有 {score_details['collision_count']} 次碰撞")

    print("=" * 60)
